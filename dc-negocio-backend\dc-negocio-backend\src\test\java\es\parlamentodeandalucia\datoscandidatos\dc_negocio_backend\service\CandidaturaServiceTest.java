package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.*;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.repository.CandidaturaRepository;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.repository.CircunscripcionRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CandidaturaServiceTest {

    @Mock
    private CandidaturaRepository candidaturaRepository;

    @Mock
    private CircunscripcionRepository circunscripcionRepository;

    @Mock
    private CircunscripcionService circunscripcionService;

    @Mock
    private EstadoCandidaturaService estadoCandidaturaService;

    @Mock
    private TipoCandidaturaService tipoCandidaturaService;

    @InjectMocks
    private CandidaturaService candidaturaService;

    private CandidaturaEntity candidaturaTest;
    private FormacionPoliticaEntity formacionTest;
    private CircunscripcionEntity circunscripcionTest;

    @BeforeEach
    void setUp() {
        // Crear entidades de prueba
        formacionTest = new FormacionPoliticaEntity(1L, "Partido Popular", "PP", "PP001");
        circunscripcionTest = new CircunscripcionEntity();
        circunscripcionTest.setId(1L);
        circunscripcionTest.setNombre("Sevilla");
        circunscripcionTest.setCandidaturaActiva(true);

        candidaturaTest = new CandidaturaEntity(formacionTest, circunscripcionTest, 1, "testuser");
        candidaturaTest.setId(1L);
        candidaturaTest.setFechaCreacion(LocalDate.now());
    }

    @Test
    void testFindAll() {
        // Given
        List<CandidaturaEntity> candidaturas = Arrays.asList(candidaturaTest);
        when(candidaturaRepository.findAllOrderedByFormacionAndCircunscripcion()).thenReturn(candidaturas);

        // When
        List<CandidaturaEntity> result = candidaturaService.findAll();

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(candidaturaTest.getId(), result.get(0).getId());
        verify(candidaturaRepository).findAllOrderedByFormacionAndCircunscripcion();
    }

    @Test
    void testFindById() {
        // Given
        when(candidaturaRepository.findById(1L)).thenReturn(Optional.of(candidaturaTest));

        // When
        Optional<CandidaturaEntity> result = candidaturaService.findById(1L);

        // Then
        assertTrue(result.isPresent());
        assertEquals(candidaturaTest.getId(), result.get().getId());
        verify(candidaturaRepository).findById(1L);
    }

    @Test
    void testSave_NuevaCandidatura() {
        // Given
        CandidaturaEntity nuevaCandidatura = new CandidaturaEntity(formacionTest, circunscripcionTest, 1, "testuser");
        nuevaCandidatura.setId(null); // Simular nueva candidatura

        EstadoCandidaturaEntity estadoManual = new EstadoCandidaturaEntity(1L, "MANUAL");
        TipoCandidaturaEntity tipoManual = new TipoCandidaturaEntity(1L, "MANUAL");

        when(estadoCandidaturaService.getManual()).thenReturn(Optional.of(estadoManual));
        when(tipoCandidaturaService.getManual()).thenReturn(Optional.of(tipoManual));
        when(candidaturaRepository.save(any(CandidaturaEntity.class))).thenReturn(nuevaCandidatura);

        // When
        CandidaturaEntity result = candidaturaService.save(nuevaCandidatura);

        // Then
        assertNotNull(result);
        verify(candidaturaRepository).save(any(CandidaturaEntity.class));
    }

    @Test
    void testSave_CandidaturaExistente() {
        // Given
        candidaturaTest.setId(1L); // Simular candidatura existente
        when(candidaturaRepository.save(candidaturaTest)).thenReturn(candidaturaTest);

        // When
        CandidaturaEntity result = candidaturaService.save(candidaturaTest);

        // Then
        assertNotNull(result);
        assertEquals(candidaturaTest.getId(), result.getId());
        verify(candidaturaRepository).save(candidaturaTest);
    }

    @Test
    void testDeleteById_Exitoso() {
        // Given
        when(candidaturaRepository.findById(1L)).thenReturn(Optional.of(candidaturaTest));
        when(circunscripcionRepository.findByIdConCandidaturas(1L)).thenReturn(Optional.of(circunscripcionTest));

        // When
        assertDoesNotThrow(() -> candidaturaService.deleteById(1L));

        // Then
        verify(candidaturaRepository).findById(1L);
        verify(candidaturaRepository).delete(candidaturaTest);
        verify(circunscripcionRepository).findByIdConCandidaturas(1L);
    }

    @Test
    void testDeleteById_CandidaturaNoEncontrada() {
        // Given
        when(candidaturaRepository.findById(1L)).thenReturn(Optional.empty());

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class,
            () -> candidaturaService.deleteById(1L));

        assertEquals("Candidatura no encontrada con ID: 1", exception.getMessage());
        verify(candidaturaRepository).findById(1L);
        verify(candidaturaRepository, never()).delete(any());
    }

    @Test
    void testFindByFormacionPoliticaAndCircunscripcion() {
        // Given
        when(candidaturaRepository.findByFormacionPoliticaIdAndCircunscripcionId(1L, 1L))
            .thenReturn(Optional.of(candidaturaTest));

        // When
        Optional<CandidaturaEntity> result = candidaturaService.findByFormacionPoliticaAndCircunscripcion(1L, 1L);

        // Then
        assertTrue(result.isPresent());
        assertEquals(candidaturaTest.getId(), result.get().getId());
        verify(candidaturaRepository).findByFormacionPoliticaIdAndCircunscripcionId(1L, 1L);
    }

    @Test
    void testValidarCandidatura() {
        // Given
        EstadoCandidaturaEntity estadoValidado = new EstadoCandidaturaEntity(2L, "VALIDADA_POR_JEA");
        when(candidaturaRepository.findById(1L)).thenReturn(Optional.of(candidaturaTest));
        when(estadoCandidaturaService.getValidada()).thenReturn(Optional.of(estadoValidado));

        // Configurar el candidaturaTest para que devuelva los valores esperados
        candidaturaTest.setEstadoCandidatura(estadoValidado);
        candidaturaTest.setUsuarioValidacion("admin");
        // Campos eliminados - no existen en BD:
        // candidaturaTest.setComentarioValidacion("Validación correcta");
        // candidaturaTest.setFechaValidacion(LocalDate.now());

        when(candidaturaRepository.save(any(CandidaturaEntity.class))).thenReturn(candidaturaTest);

        // When
        CandidaturaEntity result = candidaturaService.validarCandidatura(1L, "admin", "Validación correcta");

        // Then
        assertNotNull(result);
        verify(candidaturaRepository).save(any(CandidaturaEntity.class));
    }

    @Test
    void testRechazarCandidatura() {
        // Given
        EstadoCandidaturaEntity estadoRechazado = new EstadoCandidaturaEntity(3L, "RECHAZADA");
        when(candidaturaRepository.findById(1L)).thenReturn(Optional.of(candidaturaTest));
        when(estadoCandidaturaService.getRechazada()).thenReturn(Optional.of(estadoRechazado));

        // Configurar el candidaturaTest para que devuelva los valores esperados
        candidaturaTest.setEstadoCandidatura(estadoRechazado);
        candidaturaTest.setUsuarioValidacion("admin");
        // Campos eliminados - no existen en BD:
        // candidaturaTest.setObservacionRechazo("Datos incorrectos");
        // candidaturaTest.setFechaValidacion(LocalDate.now());

        when(candidaturaRepository.save(any(CandidaturaEntity.class))).thenReturn(candidaturaTest);

        // When
        CandidaturaEntity result = candidaturaService.rechazarCandidatura(1L, "admin", "Datos incorrectos");

        // Then
        assertNotNull(result);
        verify(candidaturaRepository).save(any(CandidaturaEntity.class));
    }

    @Test
    void testValidarCandidatura_CandidaturaNoEncontrada() {
        // Given
        when(candidaturaRepository.findById(1L)).thenReturn(Optional.empty());

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, 
            () -> candidaturaService.validarCandidatura(1L, "admin", "comentario"));
        
        assertEquals("Candidatura no encontrada", exception.getMessage());
        verify(candidaturaRepository).findById(1L);
        verify(candidaturaRepository, never()).save(any());
    }

    @Test
    void testCountByEstado() {
        // Given
        when(candidaturaRepository.countByEstadoCandidaturaId(1L)).thenReturn(5L);

        // When
        Long result = candidaturaService.countByEstado(1L);

        // Then
        assertEquals(5L, result);
        verify(candidaturaRepository).countByEstadoCandidaturaId(1L);
    }
}
