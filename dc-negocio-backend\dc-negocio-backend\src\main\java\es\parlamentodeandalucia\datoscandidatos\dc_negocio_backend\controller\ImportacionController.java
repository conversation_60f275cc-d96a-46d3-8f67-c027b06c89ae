package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.controller;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.ImportacionEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.ErrorImportEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service.ImportacionService;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service.ConvocatoriaService;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.repository.ErrorImportRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/importaciones")
@CrossOrigin(origins = { "http://localhost:4200", "https://localhost:4200" })
public class ImportacionController {

    @Autowired
    private ImportacionService importacionService;

    @Autowired
    private ErrorImportRepository errorImportRepository;

    @Autowired
    private ConvocatoriaService convocatoriaService;

    /**
     * Obtiene todas las importaciones (solo para administradores)
     */
    @GetMapping
    @PreAuthorize("hasRole('ADMINISTRADOR_DEL_SISTEMA') or hasRole('ADMINISTRADOR_DE_JEA')")
    public ResponseEntity<List<ImportacionEntity>> getAll() {
        return ResponseEntity.ok(importacionService.findAll());
    }

    /**
     * Obtiene una importación por ID (solo para administradores y representantes)
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMINISTRADOR_DEL_SISTEMA') or hasRole('ADMINISTRADOR_DE_JEA') or hasRole('REPRESENTANTE')")
    public ResponseEntity<ImportacionEntity> getById(@PathVariable Long id, @AuthenticationPrincipal Jwt jwt) {
        return importacionService.findById(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * Busca importaciones con filtros y paginación
     */
    @GetMapping("/buscar")
    @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    public ResponseEntity<Page<ImportacionEntity>> buscarConFiltros(
            @RequestParam(required = false) String usuarioImportacion,
            @RequestParam(required = false) String formatoFichero,
            @RequestParam(required = false) Boolean importado,
            Pageable pageable) {
        Page<ImportacionEntity> importaciones = importacionService.findWithFilters(
            usuarioImportacion, formatoFichero, importado, pageable);
        return ResponseEntity.ok(importaciones);
    }

    /**
     * Obtiene importaciones por usuario
     */
    @GetMapping("/usuario/{usuario}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    public ResponseEntity<List<ImportacionEntity>> getByUsuario(@PathVariable String usuario) {
        return ResponseEntity.ok(importacionService.findByUsuario(usuario));
    }

    /**
     * Obtiene importaciones con errores
     */
    @GetMapping("/con-errores")
    @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    public ResponseEntity<List<ImportacionEntity>> getConErrores() {
        return ResponseEntity.ok(importacionService.findImportacionesConErrores());
    }

    /**
     * Obtiene importaciones exitosas
     */
    @GetMapping("/exitosas")
    @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    public ResponseEntity<List<ImportacionEntity>> getExitosas() {
        return ResponseEntity.ok(importacionService.findImportacionesExitosas());
    }

    /**
     * Obtiene estadísticas de importaciones por usuario
     */
    @GetMapping("/estadisticas/{usuario}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    public ResponseEntity<Map<String, Object>> getEstadisticasPorUsuario(@PathVariable String usuario) {
        Object[] stats = importacionService.getEstadisticasPorUsuario(usuario);

        Map<String, Object> estadisticas = new HashMap<>();
        estadisticas.put("total", stats[0]);
        estadisticas.put("exitosas", stats[1]);
        estadisticas.put("conErrores", stats[2]);

        return ResponseEntity.ok(estadisticas);
    }

    /**
     * Procesa un archivo CSV de importación (solo para administradores)
     */
    @PostMapping("/procesar-csv")
    @PreAuthorize("hasRole('ADMIN') or hasRole('ADMINISTRADOR_DE_JEA')")
    public ResponseEntity<Map<String, Object>> procesarArchivoCsv(
            @RequestParam("archivo") MultipartFile archivo,
            @AuthenticationPrincipal Jwt jwt,
            HttpServletRequest request) {

        try {
            String usuario = jwt.getClaimAsString("username");
            String ip = getClientIpAddress(request);

            // Validar período electoral activo
            if (!convocatoriaService.hayConvocatoriaActiva()) {
                return ResponseEntity.badRequest()
                        .body(Map.of("error", "No hay período electoral activo para importar candidaturas"));
            }

            // Validaciones básicas del archivo
            if (archivo.isEmpty()) {
                return ResponseEntity.badRequest()
                        .body(Map.of("error", "El archivo está vacío"));
            }

            // Validar que sea un archivo CSV
            String nombreArchivo = archivo.getOriginalFilename();
            if (nombreArchivo == null || !nombreArchivo.endsWith(".csv")) {
                return ResponseEntity.badRequest()
                        .body(Map.of("error", "El archivo debe ser un CSV (.csv)"));
            }

            // Procesar la importación (formación política se detecta automáticamente del CSV)
            ImportacionEntity importacion = importacionService.procesarArchivoCsv(archivo, usuario, ip, null);

            // Preparar respuesta con resumen
            Map<String, Object> respuesta = new HashMap<>();
            respuesta.put("importacionId", importacion.getId());
            respuesta.put("nombreFichero", importacion.getNombreFichero());
            respuesta.put("formatoFichero", importacion.getFormatoFichero());
            respuesta.put("filasCorrectas", importacion.getFilasCorrectas());
            respuesta.put("filasIncorrectas", importacion.getFilasIncorrectas());

            // Obtener errores si los hay
            List<ErrorImportEntity> errores = errorImportRepository.findByImportacionId(importacion.getId());
            respuesta.put("errores", errores);
            respuesta.put("totalErrores", errores.size());

            return ResponseEntity.ok(respuesta);

        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("error", "Error al procesar el archivo: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }


    /**
     * Obtiene errores de una importación específica
     */
    @GetMapping("/{id}/errores")
    @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    public ResponseEntity<List<ErrorImportEntity>> getErroresPorImportacion(@PathVariable Long id) {
        List<ErrorImportEntity> errores = errorImportRepository.findByImportacionId(id);
        return ResponseEntity.ok(errores);
    }

    /**
     * Obtiene resumen de errores por campo para una importación
     */
    @GetMapping("/{id}/errores/resumen")
    @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    public ResponseEntity<Map<String, Object>> getResumenErrores(@PathVariable Long id) {
        List<Object[]> resumen = errorImportRepository.getResumenErroresPorCampo(id);

        Map<String, Object> resultado = new HashMap<>();
        Map<String, Long> erroresPorCampo = new HashMap<>();

        for (Object[] fila : resumen) {
            erroresPorCampo.put((String) fila[0], (Long) fila[1]);
        }

        resultado.put("erroresPorCampo", erroresPorCampo);
        resultado.put("totalErrores", errorImportRepository.countByImportacionId(id));

        return ResponseEntity.ok(resultado);
    }

    /**
     * Elimina una importación y sus errores asociados
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> delete(@PathVariable Long id) {
        if (importacionService.findById(id).isPresent()) {
            importacionService.deleteById(id);
            return ResponseEntity.ok().build();
        }
        return ResponseEntity.notFound().build();
    }

    /**
     * Obtiene la dirección IP del cliente
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedForHeader = request.getHeader("X-Forwarded-For");
        if (xForwardedForHeader == null) {
            return request.getRemoteAddr();
        } else {
            return xForwardedForHeader.split(",")[0];
        }
    }


}

