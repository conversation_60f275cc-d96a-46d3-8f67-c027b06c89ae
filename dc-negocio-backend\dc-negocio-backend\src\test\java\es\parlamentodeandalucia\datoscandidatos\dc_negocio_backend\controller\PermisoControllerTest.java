package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.controller;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.verify;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;

import java.util.Arrays;
import java.util.Optional;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;

import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import com.fasterxml.jackson.databind.ObjectMapper;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.FuncionalidadEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.PermisoEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service.PermisoService;

@SpringBootTest
@AutoConfigureMockMvc
class PermisoControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private PermisoService permisoService;

    @Autowired
    private ObjectMapper objectMapper;

    private PermisoEntity permiso;
    
    private FuncionalidadEntity funcionalidad;

    @BeforeEach
    void setUp() {
    	
    	funcionalidad = new FuncionalidadEntity();
    	funcionalidad.setId(1L);
    	funcionalidad.setNombre("test");
    	funcionalidad.setActivo(true);
    	
        permiso = new PermisoEntity();
        permiso.setId(1L);
        permiso.setNombre("Crear");
        permiso.setActivo(true);
        permiso.setFuncionalidad(funcionalidad);
    }

    @Test
    @WithMockUser(roles = {"ADMIN"})
    void testGetAllPermisos() throws Exception {
        when(permisoService.findAll()).thenReturn(Arrays.asList(permiso));

        mockMvc.perform(get("/api/permisos"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[0].id").value(1L))
                .andExpect(jsonPath("$[0].nombre").value("Crear"));
    }

    @Test
    @WithMockUser(roles = {"ADMIN"})
    void testGetPermisoById_found() throws Exception {
        when(permisoService.findById(1L)).thenReturn(Optional.of(permiso));

        mockMvc.perform(get("/api/permisos/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(1L))
                .andExpect(jsonPath("$.nombre").value("Crear"));
    }

    @Test
    @WithMockUser(roles = {"ADMIN"})
    void testGetPermisoById_notFound() throws Exception {
        when(permisoService.findById(99L)).thenReturn(Optional.empty());

        mockMvc.perform(get("/api/permisos/99"))
                .andExpect(status().isNotFound());
    }

    @Test
    @WithMockUser(roles = {"ADMIN"})
    void testCreatePermiso() throws Exception {
        when(permisoService.save(any())).thenReturn(permiso);

        mockMvc.perform(post("/api/permisos")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(permiso)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.id").value(1L))
                .andExpect(jsonPath("$.nombre").value("Crear"));
    }

    @Test
    @WithMockUser(roles = {"ADMIN"})
    void testUpdatePermiso() throws Exception {
        PermisoEntity updated = new PermisoEntity();
        updated.setId(1L);
        updated.setNombre("Editar");

        when(permisoService.updatePermiso(eq(1L), any())).thenReturn(updated);

        mockMvc.perform(put("/api/permisos/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updated)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.nombre").value("Editar"));
    }

    @Test
    @WithMockUser(roles = {"ADMIN"})
    void testDeletePermiso() throws Exception {
        Long id = 1L;

        when(permisoService.findById(id)).thenReturn(Optional.of(permiso));

        mockMvc.perform(delete("/api/permisos/{id}", id))
                .andExpect(status().isNoContent());

        verify(permisoService).deleteById(id);
    }
}