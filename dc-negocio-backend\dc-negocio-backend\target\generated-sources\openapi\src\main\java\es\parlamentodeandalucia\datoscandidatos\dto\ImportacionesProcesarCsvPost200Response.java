package es.parlamentodeandalucia.datoscandidatos.dto;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import es.parlamentodeandalucia.datoscandidatos.dto.ImportacionesProcesarCsvPost200ResponseErroresInner;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import org.hibernate.validator.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import jakarta.annotation.Generated;

/**
 * ImportacionesProcesarCsvPost200Response
 */

@JsonTypeName("_importaciones_procesar_csv_post_200_response")
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2025-08-18T12:09:52.242926500+02:00[Europe/Madrid]")
public class ImportacionesProcesarCsvPost200Response {

  private Long importacionId;

  private String formatoFichero;

  private Boolean importado;

  private Long candidaturasValidas;

  private Long candidatosValidos;

  @Valid
  private List<@Valid ImportacionesProcesarCsvPost200ResponseErroresInner> errores;

  private Integer totalErrores;

  public ImportacionesProcesarCsvPost200Response importacionId(Long importacionId) {
    this.importacionId = importacionId;
    return this;
  }

  /**
   * Get importacionId
   * @return importacionId
  */
  
  @Schema(name = "importacionId", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("importacionId")
  public Long getImportacionId() {
    return importacionId;
  }

  public void setImportacionId(Long importacionId) {
    this.importacionId = importacionId;
  }

  public ImportacionesProcesarCsvPost200Response formatoFichero(String formatoFichero) {
    this.formatoFichero = formatoFichero;
    return this;
  }

  /**
   * Get formatoFichero
   * @return formatoFichero
  */
  
  @Schema(name = "formatoFichero", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("formatoFichero")
  public String getFormatoFichero() {
    return formatoFichero;
  }

  public void setFormatoFichero(String formatoFichero) {
    this.formatoFichero = formatoFichero;
  }

  public ImportacionesProcesarCsvPost200Response importado(Boolean importado) {
    this.importado = importado;
    return this;
  }

  /**
   * Get importado
   * @return importado
  */
  
  @Schema(name = "importado", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("importado")
  public Boolean getImportado() {
    return importado;
  }

  public void setImportado(Boolean importado) {
    this.importado = importado;
  }

  public ImportacionesProcesarCsvPost200Response candidaturasValidas(Long candidaturasValidas) {
    this.candidaturasValidas = candidaturasValidas;
    return this;
  }

  /**
   * Get candidaturasValidas
   * @return candidaturasValidas
  */
  
  @Schema(name = "candidaturasValidas", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("candidaturasValidas")
  public Long getCandidaturasValidas() {
    return candidaturasValidas;
  }

  public void setCandidaturasValidas(Long candidaturasValidas) {
    this.candidaturasValidas = candidaturasValidas;
  }

  public ImportacionesProcesarCsvPost200Response candidatosValidos(Long candidatosValidos) {
    this.candidatosValidos = candidatosValidos;
    return this;
  }

  /**
   * Get candidatosValidos
   * @return candidatosValidos
  */
  
  @Schema(name = "candidatosValidos", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("candidatosValidos")
  public Long getCandidatosValidos() {
    return candidatosValidos;
  }

  public void setCandidatosValidos(Long candidatosValidos) {
    this.candidatosValidos = candidatosValidos;
  }

  public ImportacionesProcesarCsvPost200Response errores(List<@Valid ImportacionesProcesarCsvPost200ResponseErroresInner> errores) {
    this.errores = errores;
    return this;
  }

  public ImportacionesProcesarCsvPost200Response addErroresItem(ImportacionesProcesarCsvPost200ResponseErroresInner erroresItem) {
    if (this.errores == null) {
      this.errores = new ArrayList<>();
    }
    this.errores.add(erroresItem);
    return this;
  }

  /**
   * Get errores
   * @return errores
  */
  @Valid 
  @Schema(name = "errores", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("errores")
  public List<@Valid ImportacionesProcesarCsvPost200ResponseErroresInner> getErrores() {
    return errores;
  }

  public void setErrores(List<@Valid ImportacionesProcesarCsvPost200ResponseErroresInner> errores) {
    this.errores = errores;
  }

  public ImportacionesProcesarCsvPost200Response totalErrores(Integer totalErrores) {
    this.totalErrores = totalErrores;
    return this;
  }

  /**
   * Get totalErrores
   * @return totalErrores
  */
  
  @Schema(name = "totalErrores", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("totalErrores")
  public Integer getTotalErrores() {
    return totalErrores;
  }

  public void setTotalErrores(Integer totalErrores) {
    this.totalErrores = totalErrores;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ImportacionesProcesarCsvPost200Response importacionesProcesarCsvPost200Response = (ImportacionesProcesarCsvPost200Response) o;
    return Objects.equals(this.importacionId, importacionesProcesarCsvPost200Response.importacionId) &&
        Objects.equals(this.formatoFichero, importacionesProcesarCsvPost200Response.formatoFichero) &&
        Objects.equals(this.importado, importacionesProcesarCsvPost200Response.importado) &&
        Objects.equals(this.candidaturasValidas, importacionesProcesarCsvPost200Response.candidaturasValidas) &&
        Objects.equals(this.candidatosValidos, importacionesProcesarCsvPost200Response.candidatosValidos) &&
        Objects.equals(this.errores, importacionesProcesarCsvPost200Response.errores) &&
        Objects.equals(this.totalErrores, importacionesProcesarCsvPost200Response.totalErrores);
  }

  @Override
  public int hashCode() {
    return Objects.hash(importacionId, formatoFichero, importado, candidaturasValidas, candidatosValidos, errores, totalErrores);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ImportacionesProcesarCsvPost200Response {\n");
    sb.append("    importacionId: ").append(toIndentedString(importacionId)).append("\n");
    sb.append("    formatoFichero: ").append(toIndentedString(formatoFichero)).append("\n");
    sb.append("    importado: ").append(toIndentedString(importado)).append("\n");
    sb.append("    candidaturasValidas: ").append(toIndentedString(candidaturasValidas)).append("\n");
    sb.append("    candidatosValidos: ").append(toIndentedString(candidatosValidos)).append("\n");
    sb.append("    errores: ").append(toIndentedString(errores)).append("\n");
    sb.append("    totalErrores: ").append(toIndentedString(totalErrores)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

