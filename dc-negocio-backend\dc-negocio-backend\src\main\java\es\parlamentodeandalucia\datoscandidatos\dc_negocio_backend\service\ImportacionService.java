package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.*;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.repository.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
public class ImportacionService {

    @Autowired
    private ImportacionRepository importacionRepository;

    @Autowired
    private ErrorImportRepository errorImportRepository;

    @Autowired
    private CsvImportService csvImportService;

    /**
     * Obtiene todas las importaciones
     */
    public List<ImportacionEntity> findAll() {
        return importacionRepository.findAll();
    }

    /**
     * Busca una importación por ID
     */
    public Optional<ImportacionEntity> findById(Long id) {
        return importacionRepository.findById(id);
    }

    /**
     * Busca importaciones con filtros y paginación
     */
    public Page<ImportacionEntity> findWithFilters(String usuarioImportacion, String formatoFichero, 
                                                  Boolean importado, Pageable pageable) {
        return importacionRepository.findWithFilters(usuarioImportacion, formatoFichero, importado, pageable);
    }

    /**
     * Busca importaciones por usuario
     */
    public List<ImportacionEntity> findByUsuario(String usuarioImportacion) {
        return importacionRepository.findByUsuarioImportacion(usuarioImportacion);
    }

    /**
     * Busca importaciones con errores
     */
    public List<ImportacionEntity> findImportacionesConErrores() {
        return importacionRepository.findImportacionesConErrores();
    }

    /**
     * Busca importaciones exitosas
     */
    public List<ImportacionEntity> findImportacionesExitosas() {
        return importacionRepository.findImportacionesExitosas();
    }

    /**
     * Obtiene estadísticas de importaciones por usuario
     */
    public Object[] getEstadisticasPorUsuario(String usuarioImportacion) {
        return importacionRepository.getEstadisticasPorUsuario(usuarioImportacion);
    }

    /**
     * Procesa un archivo CSV de importación (sin transacción global)
     */
    public ImportacionEntity procesarArchivoCsv(MultipartFile archivo, String usuarioImportacion, String ip, Long formacionPoliticaId) {
        try {
            // Validar que sea un archivo CSV
            String nombreArchivo = archivo.getOriginalFilename();
            if (nombreArchivo == null || !nombreArchivo.endsWith(".csv")) {
                throw new IllegalArgumentException("El archivo debe ser un CSV (.csv)");
            }

            // Crear registro de importación
            ImportacionEntity importacion = new ImportacionEntity(
                usuarioImportacion,
                LocalDate.now(),
                nombreArchivo,
                "CSV",
                archivo.getBytes()
            );

            importacion = guardarImportacion(importacion);

            // Procesar archivo CSV usando el servicio especializado
            Map<String, Object> resultado = csvImportService.procesarCsv(archivo, usuarioImportacion, formacionPoliticaId);

            // Actualizar contadores
            Integer candidatosCreados = (Integer) resultado.get("candidatosCreados");
            Integer candidaturasCreadas = (Integer) resultado.get("candidaturasCreadas");

            importacion.setFilasCorrectas(candidatosCreados != null ? candidatosCreados.longValue() : 0L);
            importacion.setFilasIncorrectas(0L); // Se calculará basado en errores

            // Registrar errores si los hay
            @SuppressWarnings("unchecked")
            List<String> errores = (List<String>) resultado.get("errores");
            if (errores != null && !errores.isEmpty()) {
                importacion.setFilasIncorrectas((long) errores.size());
                for (int i = 0; i < errores.size(); i++) {
                    registrarError(importacion, (long) (i + 1), "PROCESAMIENTO", errores.get(i));
                }
            }

            return actualizarImportacion(importacion);

        } catch (IOException e) {
            throw new RuntimeException("Error al procesar el archivo", e);
        }
    }

    /**
     * Guarda una importación en transacción separada
     */
    @Transactional
    private ImportacionEntity guardarImportacion(ImportacionEntity importacion) {
        return importacionRepository.save(importacion);
    }

    /**
     * Actualiza una importación en transacción separada
     */
    @Transactional
    private ImportacionEntity actualizarImportacion(ImportacionEntity importacion) {
        return importacionRepository.save(importacion);
    }





    /**
     * Registra un error de importación
     */
    private void registrarError(ImportacionEntity importacion, Long linea, String campo, String error) {
        ErrorImportEntity errorEntity = new ErrorImportEntity(importacion, linea, campo, error);
        errorImportRepository.save(errorEntity);
    }

    /**
     * Elimina una importación y sus errores asociados
     */
    public void deleteById(Long id) {
        ImportacionEntity importacion = findById(id)
            .orElseThrow(() -> new RuntimeException("Importación no encontrada"));
        
        // Eliminar errores asociados
        errorImportRepository.deleteByImportacion(importacion);
        
        // Eliminar importación
        importacionRepository.delete(importacion);
    }
}
