.app-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #ECEDE8;
  border-top: 4px solid #C2E038;
  padding: 0 20px;
  height: auto;
  min-height: 60px;
  font-family: "Open Sans", sans-serif;
  position: relative;
  z-index: 10;

  a,
  button {
    font-size: 14px;
    line-height: 21px;
    border-radius: 4px;
    padding: 8px 12px;
    color: #333333;
    text-decoration: none;
    background: none;
    border: none;
    cursor: pointer;
  }

  .brand {
    display: flex;
    align-items: center;
    max-width: 200px;
    overflow: hidden;

    img {
      height: 40px;
      width: auto;
      object-fit: contain;
    }
  }

  .main-nav {
    display: flex;
    align-items: center;
    gap: 30px;

    .nav-link {
      display: inline-flex;
      align-items: center;
      gap: 8px;
      padding: 8px 12px;
      border-radius: 4px;
      color: #333;
      text-decoration: none;

      &.active {
        background-color: #C2E038;
        color: #FFFFFF;
      }

      &:hover {
        background-color: #A8CB5B;
        color: #FFFFFF;
      }

      &:focus-visible {
        outline: none;
        background-color: #FAFFF0;
      }
    }

    .dropdown {
      position: relative;

      .admin-btn {
        display: flex;
        align-items: center;
        gap: 6px;

        i {
          font-size: 14px;
          color: #555;
        }

        &.active {
          background-color: #C2E038;
          color: #FFFFFF;
        }

        &:hover {
          background-color: #A8CB5B;
          color: #FFFFFF;
        }
      }

      .dropdown-menu {
        position: absolute;
        top: 100%;
        left: 0;
        margin-top: 6px;
        background-color: #fff;
        border: 1px solid #e5e7eb;
        border-radius: 12px;
        box-shadow: 0px 6px 16px rgba(0, 0, 0, 0.1);
        min-width: 220px;
        padding: 8px 0;
        z-index: 999;
        overflow: hidden;

        li a {
          display: flex;
          align-items: center;
          gap: 10px;
          width: 100%;
          padding: 10px 20px;
          color: #333333;
          font-size: 14px;
          transition: background-color 0.2s ease;

          i {
            font-size: 16px;
            color: #666;
          }

          &:hover,
          &:focus-visible {
            background-color: #C2E038;
            color: white;

            i {
              color: white;
            }

            outline: none;
          }
        }
      }
    }
  }

  .user-menu {
    position: relative;

    .user-btn {
      display: flex;
      align-items: center;
      gap: 8px;
      background-color: #fff;
      border: 1px solid #ddd;
      border-radius: 8px;
      padding: 6px 12px;
      color: #333;
      font-weight: 500;
      box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.05);

      &.active,
      &:hover {
        background-color: #f9f9f9;
        color: #111;
      }

      &:focus-visible {
        outline: none;
        background-color: #FAFFF0;
      }

      .avatar {
        background-color: #ccc;
        color: white;
        width: 28px;
        height: 28px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        font-weight: bold;
      }
    }

    .dropdown-menu {
      position: absolute;
      top: 100%;
      right: 0;
      margin-top: 6px;
      background-color: #fff;
      border: 1px solid #e5e7eb;
      border-radius: 12px;
      box-shadow: 0px 6px 16px rgba(0, 0, 0, 0.1);
      min-width: 220px;
      padding: 8px 0;
      z-index: 999;
      overflow: hidden;

      li a,
      li button {
        display: flex;
        align-items: center;
        gap: 10px;
        width: 100%;
        padding: 10px 20px;
        color: #333333;
        background: none;
        border: none;
        font-size: 14px;
        line-height: 21px;
        cursor: pointer;
        transition: background-color 0.2s;

        i {
          font-size: 16px;
          color: #666;
        }

        &:hover,
        &:focus-visible {
          background-color: #f9f9f9;
          outline: none;
        }

        &.active {
          background-color: #C2E038;
          color: #FFFFFF;
        }
      }

      li.danger {
        padding-top: 8px;
        border-top: 1px solid #eee;

        button {
          color: #e11d48;
          font-weight: 600;
          border: 1px solid #e11d48;
          background-color: #fff;
          border-radius: 8px;
          padding: 10px 16px;
          margin: 0 20px;
          justify-content: center;
          display: flex;
          align-items: center;
          width: calc(100% - 40px); // Alineado con el padding del menú

          i {
            margin-right: 8px;
            color: #e11d48;
          }

          &:hover {
            background-color: #fff0f2;
          }
        }
      }
    }
  }
}

.app-header .dropdown-menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-link,
.admin-btn,
.dropdown-link {
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.nav-icon,
.dropdown-icon {
  width: 16px;
  height: 16px;
  object-fit: contain;
}
@media (max-width: 480px) {
  .app-header {
    flex-direction: column;
    align-items: stretch;
    padding: 10px 12px;

    .brand {
      justify-content: center;
      width: 100%;
      margin-bottom: 8px;

      img {
        margin: 0 auto;
      }
    }

    // 🔸 Contenedor que agrupa nav + avatar
    .header-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 8px;
      flex-wrap: nowrap;
      width: 100%;
    }

    .main-nav {
      display: flex;
      flex: 1;
      flex-wrap: nowrap;
      gap: 6px;

      .nav-link,
      .admin-btn {
        font-size: 13px;
        padding: 6px 8px;
        white-space: nowrap;
      }
    }

    .user-menu {
      margin: 0;
      flex-shrink: 0;

      .user-btn {
        font-size: 0;
        padding: 4px 6px;
        display: inline-flex;
        align-items: center;
        gap: 4px;

        .avatar {
          font-size: 13px;
          width: 24px;
          height: 24px;
        }

        .arrow {
          font-size: 12px;
        }
      }

      .dropdown-menu {
        min-width: 180px;

        li a,
        li button {
          font-size: 13px;
          padding: 8px 16px;

          i {
            font-size: 14px;
          }
        }

        li.danger button {
          font-size: 13px;
          padding: 8px 16px;
        }
      }
    }
  }
}
