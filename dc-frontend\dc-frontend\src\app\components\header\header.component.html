<header class="app-header">
  <div class="brand">
    <img src="assets/juntaElectoral-logo.png" alt="Junta Electoral de Andalucía">
  </div>
  <nav class="main-nav">
    <a *ngFor="let item of mainMenu" class="nav-link" [routerLink]="item.route" routerLinkActive="active"
      [routerLinkActiveOptions]="{ exact: true }">
      {{ item.label }}
    </a>

    <div class="dropdown">
  <button class="admin-btn" [class.active]="showAdmin" (click)="toggleAdmin()">
    <i class="fas fa-gear"></i> Administración <span class="arrow">▾</span>
  </button>

  <ul *ngIf="showAdmin" class="dropdown-menu">
    <li *ngFor="let opt of adminMenu">
      <a [routerLink]="opt.route">
        <i class="fas fa-rotate"></i> {{ opt.label }}
      </a>
    </li>
  </ul>
</div>

  </nav>

  <div class="user-menu dropdown">
    <button class="user-btn" (click)="toggleUser()" [class.active]="showUser">
      <span class="avatar">P</span> Nombre de usuario <span class="arrow">▾</span>
    </button>

    <ul *ngIf="showUser" class="dropdown-menu">
      <li>
        <a [routerLink]="'/perfil'">
          <i class="fas fa-user"></i> Mi perfil
        </a>
      </li>
      <li>
        <a [routerLink]="'/configuracion'">
          <i class="fas fa-cog"></i> Configuración
        </a>
      </li>
      <li class="danger">
        <button (click)="abrirModalLogout()">
          <i class="fas fa-sign-out-alt"></i> Cerrar sesión
        </button>
      </li>
    </ul>
  </div>
</header>

<router-outlet></router-outlet>
<app-modal-confirmar-logout [visible]="modalLogoutVisible" (cerrar)="cerrarModalLogout()"
  (logoutFrontend)="logoutFrontend()" (logoutKeycloak)="logoutKeycloak()"></app-modal-confirmar-logout>