INSERT INTO public.dac_t_funcionalidad
(dac_id_funcionalidad, dac_tx_nombre, dac_bo_activo)
VALUES(nextval('dac_t_funcionalidad_dac_id_funcionalidad_seq'::regclass), 'Gestión de usuarios', true);
INSERT INTO public.dac_t_funcionalidad
(dac_id_funcionalidad, dac_tx_nombre, dac_bo_activo)
VALUES(nextval('dac_t_funcionalidad_dac_id_funcionalidad_seq'::regclass), 'Gestión de roles', true);
INSERT INTO public.dac_t_funcionalidad
(dac_id_funcionalidad, dac_tx_nombre, dac_bo_activo)
VALUES(nextval('dac_t_funcionalidad_dac_id_funcionalidad_seq'::regclass), 'Gestión de permisos', true);
INSERT INTO public.dac_t_funcionalidad
(dac_id_funcionalidad, dac_tx_nombre, dac_bo_activo)
VALUES(nextval('dac_t_funcionalidad_dac_id_funcionalidad_seq'::regclass), 'Gestión de funcionalidades', true);
INSERT INTO public.dac_t_funcionalidad
(dac_id_funcionalidad, dac_tx_nombre, dac_bo_activo)
VALUES(nextval('dac_t_funcionalidad_dac_id_funcionalidad_seq'::regclass), 'Importación de datos', true);
INSERT INTO public.dac_t_funcionalidad
(dac_id_funcionalidad, dac_tx_nombre, dac_bo_activo)
VALUES(nextval('dac_t_funcionalidad_dac_id_funcionalidad_seq'::regclass), 'Gestión de logs', true);
INSERT INTO public.dac_t_funcionalidad
(dac_id_funcionalidad, dac_tx_nombre, dac_bo_activo)
VALUES(nextval('dac_t_funcionalidad_dac_id_funcionalidad_seq'::regclass), 'Gestión de convocatorias', true);
INSERT INTO public.dac_t_funcionalidad
(dac_id_funcionalidad, dac_tx_nombre, dac_bo_activo)
VALUES(nextval('dac_t_funcionalidad_dac_id_funcionalidad_seq'::regclass), 'Gestión de representantes', true);
INSERT INTO public.dac_t_funcionalidad
(dac_id_funcionalidad, dac_tx_nombre, dac_bo_activo)
VALUES(nextval('dac_t_funcionalidad_dac_id_funcionalidad_seq'::regclass), 'Gestión de candidaturas', true);
INSERT INTO public.dac_t_funcionalidad
(dac_id_funcionalidad, dac_tx_nombre, dac_bo_activo)
VALUES(nextval('dac_t_funcionalidad_dac_id_funcionalidad_seq'::regclass), 'Gestión de formaciones políticas', true);
INSERT INTO public.dac_t_funcionalidad
(dac_id_funcionalidad, dac_tx_nombre, dac_bo_activo)
VALUES(nextval('dac_t_funcionalidad_dac_id_funcionalidad_seq'::regclass), 'Generación de ficheros', true);
INSERT INTO public.dac_t_funcionalidad
(dac_id_funcionalidad, dac_tx_nombre, dac_bo_activo)
VALUES(nextval('dac_t_funcionalidad_dac_id_funcionalidad_seq'::regclass), 'Gestión de declaraciones', true);