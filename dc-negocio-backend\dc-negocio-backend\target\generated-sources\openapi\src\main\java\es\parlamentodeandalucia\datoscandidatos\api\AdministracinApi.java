/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (7.0.1).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
package es.parlamentodeandalucia.datoscandidatos.api;

import es.parlamentodeandalucia.datoscandidatos.dto.ResetConfirmacion;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import jakarta.annotation.Generated;

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2025-08-18T12:08:24.473940700+02:00[Europe/Madrid]")
@Validated
@Tag(name = "administración", description = "the administración API")
public interface AdministracinApi {

    /**
     * POST /api/reset : Resetear la base de datos
     * Elimina las declaraciones y usuarios no administrativos.
     *
     * @param resetConfirmacion  (required)
     * @return Reseteo completado correctamente (status code 200)
     *         or No autorizado o usuario no administrador (status code 403)
     *         or Confirmación inválida (status code 400)
     *         or Error interno del servidor (status code 500)
     */
    @Operation(
        operationId = "resetearBaseDeDatos",
        summary = "Resetear la base de datos",
        description = "Elimina las declaraciones y usuarios no administrativos.",
        tags = { "administración" },
        responses = {
            @ApiResponse(responseCode = "200", description = "Reseteo completado correctamente"),
            @ApiResponse(responseCode = "403", description = "No autorizado o usuario no administrador"),
            @ApiResponse(responseCode = "400", description = "Confirmación inválida"),
            @ApiResponse(responseCode = "500", description = "Error interno del servidor")
        },
        security = {
            @SecurityRequirement(name = "bearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/api/reset",
        consumes = { "application/json" }
    )
    ResponseEntity<Void> resetearBaseDeDatos(
        @Parameter(name = "ResetConfirmacion", description = "", required = true) @Valid @RequestBody ResetConfirmacion resetConfirmacion
    );

}
