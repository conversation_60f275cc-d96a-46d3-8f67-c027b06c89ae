package es.parlamentodeandalucia.datoscandidatos.dto;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import es.parlamentodeandalucia.datoscandidatos.dto.Circunscripcion;
import es.parlamentodeandalucia.datoscandidatos.dto.FormacionPolitica;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import org.hibernate.validator.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import jakarta.annotation.Generated;

/**
 * CandidaturaInput
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2025-08-18T12:09:52.242926500+02:00[Europe/Madrid]")
public class CandidaturaInput {

  private FormacionPolitica formacionPolitica;

  private Circunscripcion circunscripcion;

  private Integer orden;

  public CandidaturaInput() {
    super();
  }

  /**
   * Constructor with only required parameters
   */
  public CandidaturaInput(FormacionPolitica formacionPolitica, Circunscripcion circunscripcion, Integer orden) {
    this.formacionPolitica = formacionPolitica;
    this.circunscripcion = circunscripcion;
    this.orden = orden;
  }

  public CandidaturaInput formacionPolitica(FormacionPolitica formacionPolitica) {
    this.formacionPolitica = formacionPolitica;
    return this;
  }

  /**
   * Get formacionPolitica
   * @return formacionPolitica
  */
  @NotNull @Valid 
  @Schema(name = "formacionPolitica", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("formacionPolitica")
  public FormacionPolitica getFormacionPolitica() {
    return formacionPolitica;
  }

  public void setFormacionPolitica(FormacionPolitica formacionPolitica) {
    this.formacionPolitica = formacionPolitica;
  }

  public CandidaturaInput circunscripcion(Circunscripcion circunscripcion) {
    this.circunscripcion = circunscripcion;
    return this;
  }

  /**
   * Get circunscripcion
   * @return circunscripcion
  */
  @NotNull @Valid 
  @Schema(name = "circunscripcion", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("circunscripcion")
  public Circunscripcion getCircunscripcion() {
    return circunscripcion;
  }

  public void setCircunscripcion(Circunscripcion circunscripcion) {
    this.circunscripcion = circunscripcion;
  }

  public CandidaturaInput orden(Integer orden) {
    this.orden = orden;
    return this;
  }

  /**
   * Get orden
   * @return orden
  */
  @NotNull 
  @Schema(name = "orden", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("orden")
  public Integer getOrden() {
    return orden;
  }

  public void setOrden(Integer orden) {
    this.orden = orden;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CandidaturaInput candidaturaInput = (CandidaturaInput) o;
    return Objects.equals(this.formacionPolitica, candidaturaInput.formacionPolitica) &&
        Objects.equals(this.circunscripcion, candidaturaInput.circunscripcion) &&
        Objects.equals(this.orden, candidaturaInput.orden);
  }

  @Override
  public int hashCode() {
    return Objects.hash(formacionPolitica, circunscripcion, orden);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CandidaturaInput {\n");
    sb.append("    formacionPolitica: ").append(toIndentedString(formacionPolitica)).append("\n");
    sb.append("    circunscripcion: ").append(toIndentedString(circunscripcion)).append("\n");
    sb.append("    orden: ").append(toIndentedString(orden)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

