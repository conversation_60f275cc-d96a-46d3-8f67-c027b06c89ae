package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service;

import java.util.List;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.FuncionalidadEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.repository.FuncionalidadRepository;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.repository.PermisoRepository;

/**
 * Implementación del servicio para la gestión de funcionalidades.
 */
@Service
@Transactional
public class FuncionalidadService {

    private static final Logger logger = LoggerFactory.getLogger(FuncionalidadService.class);

    @Autowired
    private final FuncionalidadRepository funcionalidadRepository;
    
    @Autowired
    private PermisoRepository permisoRepository;

    public FuncionalidadService(FuncionalidadRepository funcionalidadRepository, PermisoRepository permisoRepository) {
        this.funcionalidadRepository = funcionalidadRepository;
        this.permisoRepository = permisoRepository;
    }

    /**
     * Obtiene todas las funcionalidades.
     */
    @Transactional(readOnly = true)
    public List<FuncionalidadEntity> findAll() {
        logger.info("Obteniendo todas las funcionalidades");
        return funcionalidadRepository.findAll();
    }

    /**
     * Busca una funcionalidad por su ID.
     */
    @Transactional(readOnly = true)
    public Optional<FuncionalidadEntity> findById(Long id) {
        logger.info("Buscando funcionalidad con ID {}", id);
        return funcionalidadRepository.findById(id);
    }

    /**
     * Guarda una nueva funcionalidad.
     */
    @Transactional
    public FuncionalidadEntity save(FuncionalidadEntity funcionalidad) {
        logger.info("Guardando nueva funcionalidad: {}", funcionalidad.getNombre());
        return funcionalidadRepository.save(funcionalidad);
    }

    /**
     * Actualiza una funcionalidad existente.
     */
    @Transactional
    public FuncionalidadEntity updateFuncionalidad(Long id, FuncionalidadEntity funcionalidadDetails) {
        logger.info("Actualizando funcionalidad con ID {}", id);
        FuncionalidadEntity funcionalidad = funcionalidadRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Funcionalidad no encontrada con ID: " + id));

        funcionalidad.setNombre(funcionalidadDetails.getNombre());
        funcionalidad.setActivo(funcionalidadDetails.getActivo());

        return funcionalidadRepository.save(funcionalidad);
    }

    /**
     * Elimina una funcionalidad por su ID.
     */   
    @Transactional
    public void deleteById(Long id) {
        logger.info("Eliminando funcionalidad con ID {}", id);
        FuncionalidadEntity funcionalidad = funcionalidadRepository.findById(id)
            .orElseThrow(() -> new RuntimeException("Funcionalidad no encontrada con ID: " + id));

        boolean tienePermisosAsociados = permisoRepository.existsByFuncionalidadId(id);

        if (tienePermisosAsociados) {
            throw new RuntimeException("No se puede eliminar la funcionalidad con ID " + id + " porque tiene permisos asociados.");
        }

        funcionalidadRepository.delete(funcionalidad);
    }

    /**
     * Activa o desactiva una funcionalidad según el valor indicado.
     */
    @Transactional
    public FuncionalidadEntity toggleActivo(Long id, boolean activo) {
        logger.info("{} funcionalidad con ID {}", activo ? "Activando" : "Desactivando", id);
        FuncionalidadEntity funcionalidad = funcionalidadRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Funcionalidad no encontrada con ID: " + id));

        funcionalidad.setActivo(activo);
        return funcionalidadRepository.save(funcionalidad);
    }
}
