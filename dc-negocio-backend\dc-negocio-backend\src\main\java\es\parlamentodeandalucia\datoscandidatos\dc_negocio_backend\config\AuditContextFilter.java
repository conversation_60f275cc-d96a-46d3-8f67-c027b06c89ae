package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.config;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.sql.DataSource;
import java.io.IOException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Filtro que establece el contexto del usuario actual para auditoría
 * Extrae el ID del usuario desde el JWT token y lo establece en la base de datos
 */
@Component
public class AuditContextFilter extends OncePerRequestFilter {

    @Autowired
    private DataSource dataSource;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, 
                                  FilterChain filterChain) throws ServletException, IOException {
        
        try {
            // Obtener la autenticación actual
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            
            if (authentication != null && authentication.getPrincipal() instanceof Jwt) {
                Jwt jwt = (Jwt) authentication.getPrincipal();
                
                // Extraer información del usuario desde el JWT
                String username = jwt.getClaimAsString("preferred_username");
                String keycloakId = jwt.getClaimAsString("sub");
                
                if (username != null) {
                    // Buscar el ID del usuario en la base de datos local
                    Long userId = findUserIdByUsername(username, keycloakId);
                    
                    if (userId != null) {
                        // Establecer el contexto del usuario en la base de datos
                        setUserContext(userId);
                    }
                }
            }
            
            // Continuar con la cadena de filtros
            filterChain.doFilter(request, response);
            
        } catch (Exception e) {
            logger.warn("Error al establecer contexto de auditoría: " + e.getMessage());
            // Continuar con la cadena de filtros aunque falle el contexto de auditoría
            filterChain.doFilter(request, response);
        }
    }

    /**
     * Busca o crea el usuario en la base de datos local
     * Retorna el dac_id_usuario de la tabla dac_t_usuario
     */
    private Long findUserIdByUsername(String username, String keycloakId) {
        // Primero intentar encontrar el usuario existente
        Long existingUserId = findExistingUser(username);
        if (existingUserId != null) {
            // Actualizar último acceso
            updateLastAccess(existingUserId);
            return existingUserId;
        }

        // Si no existe, crear nuevo usuario
        return createNewUser(username);
    }

    /**
     * Busca un usuario existente en la base de datos
     */
    private Long findExistingUser(String username) {
        String sql = """
            SELECT dac_id_usuario
            FROM dac_t_usuario
            WHERE dac_fk_username = ?
            LIMIT 1
            """;

        try (Connection conn = dataSource.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setString(1, username);

            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getLong("dac_id_usuario");
                }
            }
        } catch (SQLException e) {
            logger.warn("Error al buscar usuario existente: " + e.getMessage());
        }

        return null;
    }

    /**
     * Crea un nuevo usuario en la base de datos local
     */
    private Long createNewUser(String username) {
        String sql = """
            INSERT INTO dac_t_usuario (
                dac_fk_username,
                dac_bl_activo,
                dac_fh_alta
            ) VALUES (?, true, CURRENT_DATE)
            RETURNING dac_id_usuario
            """;

        try (Connection conn = dataSource.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setString(1, keycloakId);
            stmt.setString(2, username);

            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    Long newUserId = rs.getLong("dac_id_usuario");
                    logger.info("Nuevo usuario creado con ID: " + newUserId + " para username: " + username);
                    return newUserId;
                }
            }
        } catch (SQLException e) {
            logger.error("Error al crear nuevo usuario: " + e.getMessage());
        }

        return null;
    }

    /**
     * Actualiza la fecha de último acceso del usuario
     */
    private void updateLastAccess(Long userId) {
        String sql = "UPDATE dac_t_usuario SET dac_dt_ultimo_acceso = CURRENT_TIMESTAMP WHERE dac_id_usuario = ?";

        try (Connection conn = dataSource.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setLong(1, userId);
            stmt.executeUpdate();

        } catch (SQLException e) {
            logger.warn("Error al actualizar último acceso: " + e.getMessage());
        }
    }

    /**
     * Establece el contexto del usuario actual en la base de datos
     */
    private void setUserContext(Long userId) {
        String sql = "SELECT set_current_user_context(?)";
        
        try (Connection conn = dataSource.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setLong(1, userId);
            stmt.execute();
            
        } catch (SQLException e) {
            logger.warn("Error al establecer contexto de usuario: " + e.getMessage());
        }
    }

    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) throws ServletException {
        String path = request.getRequestURI();
        
        // No aplicar el filtro a rutas públicas
        return path.startsWith("/swagger-ui") ||
               path.startsWith("/api-docs") ||
               path.startsWith("/v3/api-docs") ||
               path.startsWith("/webjars") ||
               path.equals("/swagger.yaml") ||
               path.startsWith("/actuator");
    }
}
