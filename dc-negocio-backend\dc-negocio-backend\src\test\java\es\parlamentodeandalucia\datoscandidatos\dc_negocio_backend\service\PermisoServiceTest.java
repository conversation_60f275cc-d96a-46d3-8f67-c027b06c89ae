package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.FuncionalidadEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.PermisoEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.repository.PermisoRepository;

@ExtendWith(MockitoExtension.class)
public class PermisoServiceTest {

    @Mock
    private PermisoRepository permisoRepository;

    @InjectMocks
    private PermisoService permisoService;

    private PermisoEntity permiso;
    
    private FuncionalidadEntity funcionalidad;

    @BeforeEach
    void setUp() {
    	
    	funcionalidad = new FuncionalidadEntity();
    	funcionalidad.setId(1L);
    	funcionalidad.setNombre("test");
    	funcionalidad.setActivo(true);
    	
        permiso = new PermisoEntity();
        permiso.setId(1L);
        permiso.setNombre("Crear");
        permiso.setActivo(true);
        permiso.setFuncionalidad(funcionalidad);
        
    }

    @Test
    void testFindAll() {
        when(permisoRepository.findAll()).thenReturn(Arrays.asList(permiso));

        List<PermisoEntity> result = permisoService.findAll();

        assertThat(result).hasSize(1);
        assertThat(result.get(0).getNombre()).isEqualTo("Crear");
        verify(permisoRepository, times(1)).findAll();
    }

    @Test
    void testFindById_existingId() {
        when(permisoRepository.findById(1L)).thenReturn(Optional.of(permiso));

        Optional<PermisoEntity> result = permisoService.findById(1L);

        assertThat(result).isPresent();
        assertThat(result.get().getId()).isEqualTo(1L);
        verify(permisoRepository).findById(1L);
    }

    @Test
    void testFindById_nonExistingId() {
        when(permisoRepository.findById(999L)).thenReturn(Optional.empty());

        Optional<PermisoEntity> result = permisoService.findById(999L);

        assertThat(result).isNotPresent();
        verify(permisoRepository).findById(999L);
    }

    @Test
    void testSavePermiso() {
        when(permisoRepository.save(permiso)).thenReturn(permiso);

        PermisoEntity result = permisoService.save(permiso);

        assertThat(result).isNotNull();
        assertThat(result.getNombre()).isEqualTo("Crear");
        verify(permisoRepository).save(permiso);
    }

    @Test
    void testUpdatePermiso_existingId() {
        PermisoEntity updated = new PermisoEntity();
        updated.setNombre("Editar");

        when(permisoRepository.findById(1L)).thenReturn(Optional.of(permiso));
        when(permisoRepository.save(any())).thenAnswer(i -> i.getArgument(0));

        PermisoEntity result = permisoService.updatePermiso(1L, updated);

        assertThat(result.getNombre()).isEqualTo("Editar");
        verify(permisoRepository).findById(1L);
        verify(permisoRepository).save(any());
    }

    @Test
    void testUpdatePermiso_nonExistingId() {
        PermisoEntity updated = new PermisoEntity();
        updated.setNombre("Editar");

        when(permisoRepository.findById(999L)).thenReturn(Optional.empty());

        assertThatThrownBy(() -> permisoService.updatePermiso(999L, updated))
                .isInstanceOf(RuntimeException.class)
                .hasMessageContaining("Permiso no encontrado");

        verify(permisoRepository).findById(999L);
        verify(permisoRepository, never()).save(any());
    }

    @Test
    void testDeleteById_existingId() {
        when(permisoRepository.findById(1L)).thenReturn(Optional.of(permiso));

        permisoService.deleteById(1L);

        verify(permisoRepository).delete(permiso);
    }

    @Test
    void testDeleteById_nonExistingId() {
        when(permisoRepository.findById(999L)).thenReturn(Optional.empty());

        assertThatThrownBy(() -> permisoService.deleteById(999L))
                .isInstanceOf(RuntimeException.class)
                .hasMessageContaining("Permiso no encontrado");

        verify(permisoRepository, never()).delete(any());
    }
}
