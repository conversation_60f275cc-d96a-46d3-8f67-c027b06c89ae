openapi: 3.0.0
info:
  title: API Gestión de Representantes y Usuarios - Datos Candidatos
  version: 1.0.0

servers:
  - url: http://localhost:8080/api
  - url: https://***************:8463/api/v1

security:
  - bearerAuth: []

paths:
  /auth/me:
    get:
      summary: Recuperar información del usuario autenticado
      description: Obtiene la información del usuario actual desde el token JWT
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Información del usuario autenticado
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Usuario'
        '401':
          description: Token no válido o expirado

  /usuarios:
    get:
      summary: Listado de usuarios (filtros por rol, estado, etc.)
      parameters:
        - in: query
          name: rol
          schema:
            type: string
        - in: query
          name: estado
          schema:
            type: string
      responses:
        '200':
          description: Lista de usuarios
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Usuario'
    post:
      summary: Alta de usuario (admin)
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Usuario'
      responses:
        '201':
          description: Usuario creado correctamente

  /usuarios/{id}:
    get:
      summary: Consulta de usuario
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Usuario encontrado
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Usuario'
    put:
      summary: Modificación de usuario
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Usuario'
      responses:
        '200':
          description: Usuario actualizado correctamente
    delete:
      summary: Baja de usuario
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
      responses:
        '204':
          description: Usuario eliminado correctamente

  /roles:
    get:
      summary: Listado de roles disponibles
      responses:
        '200':
          description: Lista de roles
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Rol'

  /representantes:
    get:
      summary: Listado de representantes
      responses:
        '200':
          description: Lista de representantes
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Representante'
    post:
      summary: Alta de representante
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Representante'
      responses:
        '201':
          description: Representante creado correctamente

  /representantes/{id}:
    get:
      summary: Consulta de representante
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Representante encontrado
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Representante'
    put:
      summary: Modificación de representante
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Representante'
      responses:
        '200':
          description: Representante actualizado correctamente
    delete:
      summary: Eliminar representante
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
      responses:
        '204':
          description: Representante eliminado correctamente

  /representantes/{id}/candidatos:
    get:
      summary: Candidatos asociados a un representante
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Lista de candidatos asociados
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Candidato'
  /formaciones-politicas:
    get:
      summary: Listado de formaciones políticas
      responses:
        '200':
          description: Lista de formaciones políticas
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/FormacionPolitica'
    post:
      summary: Alta de formación política
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FormacionPoliticaInput'
      responses:
        '201':
          description: Formación política creada correctamente
        '400':
          description: Datos duplicados o inválidos
  /formaciones-politicas/{id}:
    get:
      summary: Obtener una formación política por ID
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: Formación política encontrada
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FormacionPolitica'
        '404':
          description: Formación política no encontrada
    put:
      summary: Modificación de formación política
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
            format: int64
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FormacionPoliticaInput'
      responses:
        '200':
          description: Formación política actualizada correctamente
        '404':
          description: Formación política no encontrada
        '400':
          description: Datos duplicados o inválidos

    delete:
      summary: Eliminación de formación política
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '204':
          description: Formación política eliminada correctamente
        '409':
          description: No se puede eliminar, tiene candidaturas asociadas
        '404':
          description: Formación política no encontrada
  /plantillas:
    get:
      summary: Listado de plantillas disponibles
      responses:
        '200':
          description: Lista de plantillas
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Plantilla'
    post:
      summary: Alta de plantilla
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Plantilla'
      responses:
        '201':
          description: Plantilla creada correctamente
        '400':
          description: Datos inválidos

  /plantillas/{id}:
    get:
      summary: Obtener una plantilla por ID
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Detalle de la plantilla
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Plantilla'
        '404':
          description: Plantilla no encontrada
    put:
      summary: Modificación de plantilla
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Plantilla'
      responses:
        '200':
          description: Plantilla actualizada correctamente
        '400':
          description: Datos inválidos
        '404':
          description: Plantilla no encontrada
    delete:
      summary: Eliminación de plantilla
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
      responses:
        '204':
          description: Plantilla eliminada correctamente
        '404':
          description: Plantilla no encontrada

  
  /circunscripciones:
    get:
      summary: Listado de circunscripciones
      responses:
        '200':
          description: Lista de circunscripciones
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Circunscripcion'
    post:
      summary: Alta de circunscripción
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Circunscripcion'
      responses:
        '201':
          description: Circunscripción creada correctamente
    
  /circunscripciones/{id}:
    get:
      summary: Consulta de circunscripción
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Circunscripción encontrada
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Circunscripcion'
    put:
      summary: Modificación de circunscripción
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Circunscripcion'   
      responses:
        '200':
          description: Circunscripción actualizada correctamente
    delete:
      summary: Baja de circunscripción
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
      responses:
        '204':
          description: Circunscripción eliminada correctamente
  /convocatorias:
    get:
      summary: Listado de convocatorias
      responses:
        '200':
          description: Lista de convocatorias
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Convocatoria'
    post:
      summary: Alta de convocatoria
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Convocatoria'
      responses:
        '201':
          description: Convocatoria creada correctamente
  
  /convocatorias/{id}:
    get: 
      summary: Consulta de convocatoria
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Convocatoria encontrada
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Convocatoria'
    put:
      summary: Modificación de convocatoria 
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
      requestBody:
        required: true  
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Convocatoria'
      responses:
        '200':
          description: Convocatoria actualizada correctamente
    delete:
      summary: Baja de convocatoria
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
      responses:
        '204':  
          description: Convocatoria eliminada correctamente

  /candidatos:
    get:
      summary: Listado de candidatos
      responses:
        '200':
          description: Lista de candidatos
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Candidato'
    post:
      summary: Alta de candidato
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Candidato'
      responses:
        '201':
          description: Candidato creado correctamente
  /api/reset:
      post:
        summary: Resetear la base de datos
        description: Elimina las declaraciones y usuarios no administrativos.
        operationId: resetearBaseDeDatos
        tags:
          - administración
        requestBody:
          required: true
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResetConfirmacion'
        responses:
          '200':
            description: Reseteo completado correctamente
          '403':
            description: No autorizado o usuario no administrador
          '400':
            description: Confirmación inválida
          '500':
            description: Error interno del servidor
        security:
          - bearerAuth: []

  # CRUD Candidaturas
  /candidaturas:
    get:
      summary: Listar todas las candidaturas
      description: Obtiene la lista completa de candidaturas con información de formación política y circunscripción
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Lista de candidaturas
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Candidatura'
        '401':
          description: No autorizado
    post:
      summary: Crear nueva candidatura
      description: Crea una nueva candidatura (requiere rol ADMIN)
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CandidaturaInput'
      responses:
        '201':
          description: Candidatura creada exitosamente
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Candidatura'
        '400':
          description: Datos inválidos
        '401':
          description: No autorizado
        '403':
          description: Sin permisos (requiere rol ADMIN)

  /candidaturas/{id}:
    get:
      summary: Obtener candidatura por ID
      description: Obtiene los detalles de una candidatura específica
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: Detalles de la candidatura
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Candidatura'
        '404':
          description: Candidatura no encontrada
        '401':
          description: No autorizado
    put:
      summary: Actualizar candidatura
      description: Actualiza una candidatura existente (requiere rol ADMIN)
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
            format: int64
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CandidaturaInput'
      responses:
        '200':
          description: Candidatura actualizada exitosamente
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Candidatura'
        '400':
          description: Datos inválidos
        '404':
          description: Candidatura no encontrada
        '401':
          description: No autorizado
        '403':
          description: Sin permisos (requiere rol ADMIN)
    delete:
      summary: Eliminar candidatura
      description: Elimina una candidatura (requiere rol ADMIN)
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '204':
          description: Candidatura eliminada exitosamente
        '404':
          description: Candidatura no encontrada
        '401':
          description: No autorizado
        '403':
          description: Sin permisos (requiere rol ADMIN)

  /candidaturas/{id}/validar:
    post:
      summary: Validar candidatura
      description: Valida una candidatura por parte de la JEA (requiere rol ADMIN)
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
            format: int64
        - in: query
          name: comentario
          required: false
          schema:
            type: string
      responses:
        '200':
          description: Candidatura validada exitosamente
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Candidatura'
        '404':
          description: Candidatura no encontrada
        '401':
          description: No autorizado
        '403':
          description: Sin permisos (requiere rol ADMIN)

  /candidaturas/{id}/rechazar:
    post:
      summary: Rechazar candidatura
      description: Rechaza una candidatura con observaciones (requiere rol ADMIN)
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
            format: int64
        - in: query
          name: observacion
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Candidatura rechazada exitosamente
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Candidatura'
        '404':
          description: Candidatura no encontrada
        '401':
          description: No autorizado
        '403':
          description: Sin permisos (requiere rol ADMIN)

  # Importación de archivos CSV
  /importaciones/procesar-csv:
    post:
      summary: Importar candidatos desde archivo CSV
      description: Procesa un archivo CSV para importar candidatos y candidaturas (requiere rol ADMIN)
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                archivo:
                  type: string
                  format: binary
                  description: Archivo CSV (.csv) con los datos de candidatos en formato específico
      responses:
        '200':
          description: Archivo procesado exitosamente
          content:
            application/json:
              schema:
                type: object
                properties:
                  importacionId:
                    type: integer
                    format: int64
                  formatoFichero:
                    type: string
                  importado:
                    type: boolean
                  candidaturasValidas:
                    type: integer
                    format: int64
                  candidatosValidos:
                    type: integer
                    format: int64
                  errores:
                    type: array
                    items:
                      type: object
                      properties:
                        linea:
                          type: integer
                          format: int64
                          description: Número de línea en el archivo donde ocurrió el error
                        campo:
                          type: string
                          description: Campo o tipo de error (ej. PROCESAMIENTO)
                        error:
                          type: string
                          description: Descripción detallada del error incluyendo los datos específicos de la entrada que falló
                  totalErrores:
                    type: integer
        '400':
          description: Archivo inválido o error en el procesamiento
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
        '401':
          description: No autorizado
        '403':
          description: Sin permisos (requiere rol ADMIN)

  /importaciones:
    get:
      summary: Listar importaciones
      description: Obtiene la lista de todas las importaciones realizadas
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Lista de importaciones
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Importacion'
        '401':
          description: No autorizado

  /importaciones/{id}:
    get:
      summary: Obtener importación por ID
      description: Obtiene los detalles de una importación específica
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: Detalles de la importación
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Importacion'
        '404':
          description: Importación no encontrada
        '401':
          description: No autorizado
    delete:
      summary: Eliminar importación
      description: Elimina una importación (requiere rol ADMIN)
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: Importación eliminada exitosamente
        '404':
          description: Importación no encontrada
        '401':
          description: No autorizado
        '403':
          description: Sin permisos (requiere rol ADMIN)

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    Usuario:
      type: object
      properties:
        id:
          type: integer
        username:
          type: string
        nombre:
          type: string
        apellido1:
          type: string
        apellido2:
          type: string
        nombre_completo:
          type: string
        email:
          type: string
        rol:
          type: string
        estado:
          type: string
          enum: [activo, inactivo, bloqueado]
    Representante:
      type: object
      properties:
        id:
          type: integer
        nombre:
          type: string
        email:
          type: string
        circunscripcion:
          type: string
    Candidato:
      type: object
      properties:
        id:
          type: integer
        nombre:
          type: string
        apellidos:
          type: string
        circunscripcion:
          type: string
        formacion_politica:
          type: string
        estado:
          type: string
          enum: [pendiente, finalizada, presentada, validada]
    Rol:
      type: object
      properties:
        id:
          type: integer
        nombre:
          type: string
    FormacionPolitica:
      type: object
      properties:
        id:
          type: integer
          format: int64
        nombre:
          type: string
        siglas:
          type: string
        codigoInterno:
          type: string

    FormacionPoliticaInput:
      type: object
      required:
        - nombre
      properties:
        nombre:
          type: string
        siglas:
          type: string
        codigoInterno:
          type: string
    
    Plantilla:
      type: object
      properties:
        id:
          type: integer
        nombre:
          type: string
        tipoDocumento:
          type: string
        activa:
          type: boolean
    Circunscripcion:
      type: object
      properties:
        id:
          type: integer
        nombre:
          type: string
        codigo:
          type: string
        provincia:
          type: string
        comunidad_autonoma:
          type: string
    Convocatoria:
      type: object
      properties:
        id:
          type: integer
        nombre:
          type: string
        fecha_inicio:
          type: string
          format: date-time
        fecha_fin:
          type: string
          format: date-time
        estado:
          type: string
          enum: [abierta, cerrada, en_revision]
    ResetConfirmacion:
      type: object
      required:
        - confirmacion1
        - confirmacion2
      properties:
        confirmacion1:
          type: boolean
        confirmacion2:
          type: boolean      

    Candidatura:
      type: object
      properties:
        id:
          type: integer
          format: int64
        orden:
          type: integer
        usuarioCreacion:
          type: string
        fechaCreacion:
          type: string
          format: date-time
        usuarioValidacion:
          type: string
        fechaValidacion:
          type: string
          format: date-time
        comentarioValidacion:
          type: string
        observacionRechazo:
          type: string
        formacionPolitica:
          $ref: '#/components/schemas/FormacionPolitica'
        circunscripcion:
          $ref: '#/components/schemas/Circunscripcion'
        estadoCandidatura:
          type: object
          properties:
            id:
              type: integer
              format: int64
            valor:
              type: string
        tipoCandidatura:
          type: object
          properties:
            id:
              type: integer
              format: int64
            valor:
              type: string
        candidatos:
          type: array
          items:
            $ref: '#/components/schemas/Candidato'

    CandidaturaInput:
      type: object
      required:
        - formacionPolitica
        - circunscripcion
        - orden
      properties:
        formacionPolitica:
          $ref: '#/components/schemas/FormacionPolitica'
        circunscripcion:
          $ref: '#/components/schemas/Circunscripcion'
        orden:
          type: integer

    Importacion:
      type: object
      properties:
        id:
          type: integer
          format: int64
        usuarioImportacion:
          type: string
        fechaImportacion:
          type: string
          format: date
        formatoFichero:
          type: string
        importado:
          type: boolean
        candidatoValido:
          type: integer
          format: int64
        candidaturaValida:
          type: integer
          format: int64
        ipImportacion:
          type: string



