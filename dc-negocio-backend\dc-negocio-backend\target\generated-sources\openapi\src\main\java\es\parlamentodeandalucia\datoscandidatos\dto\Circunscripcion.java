package es.parlamentodeandalucia.datoscandidatos.dto;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import org.hibernate.validator.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import jakarta.annotation.Generated;

/**
 * Circunscripcion
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2025-08-18T12:09:52.242926500+02:00[Europe/Madrid]")
public class Circunscripcion {

  private Integer id;

  private String nombre;

  private String codigo;

  private String provincia;

  private String comunidadAutonoma;

  public Circunscripcion id(Integer id) {
    this.id = id;
    return this;
  }

  /**
   * Get id
   * @return id
  */
  
  @Schema(name = "id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("id")
  public Integer getId() {
    return id;
  }

  public void setId(Integer id) {
    this.id = id;
  }

  public Circunscripcion nombre(String nombre) {
    this.nombre = nombre;
    return this;
  }

  /**
   * Get nombre
   * @return nombre
  */
  
  @Schema(name = "nombre", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("nombre")
  public String getNombre() {
    return nombre;
  }

  public void setNombre(String nombre) {
    this.nombre = nombre;
  }

  public Circunscripcion codigo(String codigo) {
    this.codigo = codigo;
    return this;
  }

  /**
   * Get codigo
   * @return codigo
  */
  
  @Schema(name = "codigo", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("codigo")
  public String getCodigo() {
    return codigo;
  }

  public void setCodigo(String codigo) {
    this.codigo = codigo;
  }

  public Circunscripcion provincia(String provincia) {
    this.provincia = provincia;
    return this;
  }

  /**
   * Get provincia
   * @return provincia
  */
  
  @Schema(name = "provincia", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("provincia")
  public String getProvincia() {
    return provincia;
  }

  public void setProvincia(String provincia) {
    this.provincia = provincia;
  }

  public Circunscripcion comunidadAutonoma(String comunidadAutonoma) {
    this.comunidadAutonoma = comunidadAutonoma;
    return this;
  }

  /**
   * Get comunidadAutonoma
   * @return comunidadAutonoma
  */
  
  @Schema(name = "comunidad_autonoma", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("comunidad_autonoma")
  public String getComunidadAutonoma() {
    return comunidadAutonoma;
  }

  public void setComunidadAutonoma(String comunidadAutonoma) {
    this.comunidadAutonoma = comunidadAutonoma;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Circunscripcion circunscripcion = (Circunscripcion) o;
    return Objects.equals(this.id, circunscripcion.id) &&
        Objects.equals(this.nombre, circunscripcion.nombre) &&
        Objects.equals(this.codigo, circunscripcion.codigo) &&
        Objects.equals(this.provincia, circunscripcion.provincia) &&
        Objects.equals(this.comunidadAutonoma, circunscripcion.comunidadAutonoma);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, nombre, codigo, provincia, comunidadAutonoma);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Circunscripcion {\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    nombre: ").append(toIndentedString(nombre)).append("\n");
    sb.append("    codigo: ").append(toIndentedString(codigo)).append("\n");
    sb.append("    provincia: ").append(toIndentedString(provincia)).append("\n");
    sb.append("    comunidadAutonoma: ").append(toIndentedString(comunidadAutonoma)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

