package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.FormacionPoliticaEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.repository.FormacionPoliticaRepository;
import es.parlamentodeandalucia.datoscandidatos.dto.FormacionPolitica;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class FormacionPoliticaServiceTest {

    @Mock
    private FormacionPoliticaRepository repository;

    @InjectMocks
    private FormacionPoliticaService service;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testListarTodas() {
        FormacionPoliticaEntity fp1 = new FormacionPoliticaEntity(1L, "PSOE", "PSOE", "001");
        FormacionPoliticaEntity fp2 = new FormacionPoliticaEntity(2L, "PP", "PP", "002");

        when(repository.findAll()).thenReturn(Arrays.asList(fp1, fp2));

        List<FormacionPolitica> resultado = service.listarTodas();

        assertEquals(2, resultado.size());
        assertEquals("PSOE", resultado.get(0).getNombre());
        verify(repository, times(1)).findAll();
    }

    @Test
    void testBuscarPorIdExistente() {
        FormacionPoliticaEntity fp = new FormacionPoliticaEntity(1L, "VOX", "VOX", "003");

        when(repository.findById(1L)).thenReturn(Optional.of(fp));

        Optional<FormacionPolitica> resultado = service.buscarPorId(1L);

        assertTrue(resultado.isPresent());
        assertEquals("VOX", resultado.get().getNombre());
        verify(repository).findById(1L);
    }

    @Test
    void testBuscarPorIdNoExistente() {
        when(repository.findById(99L)).thenReturn(Optional.empty());

        Optional<FormacionPolitica> resultado = service.buscarPorId(99L);

        assertFalse(resultado.isPresent());
        verify(repository).findById(99L);
    }

    @Test
    void testBuscarPorNombre() {
        FormacionPoliticaEntity fp = new FormacionPoliticaEntity(3L, "SUMAR", "SUM", "004");

        when(repository.findByNombre("SUMAR")).thenReturn(Optional.of(fp));

        Optional<FormacionPolitica> resultado = service.buscarPorNombre("SUMAR");

        assertTrue(resultado.isPresent());
        assertEquals("SUM", resultado.get().getSiglas());
        verify(repository).findByNombre("SUMAR");
    }

    @Test
    void testGuardar() {
        FormacionPolitica dto = new FormacionPolitica();
        dto.setNombre("PACMA");
        dto.setSiglas("PAC");
        dto.setCodigoInterno("999");

        FormacionPoliticaEntity entity = new FormacionPoliticaEntity(null, "PACMA", "PAC", "999");
        FormacionPoliticaEntity guardada = new FormacionPoliticaEntity(10L, "PACMA", "PAC", "999");

        when(repository.save(any(FormacionPoliticaEntity.class))).thenReturn(guardada);

        FormacionPolitica resultado = service.guardar(dto);

        assertNotNull(resultado);
        assertEquals("PACMA", resultado.getNombre());
        assertEquals(10L, resultado.getId());
        verify(repository).save(any(FormacionPoliticaEntity.class));
    }

    @Test
    void testActualizarExistente() {
        FormacionPoliticaEntity existente = new FormacionPoliticaEntity(20L, "Antiguo", "ANT", "OLD");

        FormacionPolitica dto = new FormacionPolitica();
        dto.setNombre("Nuevo");
        dto.setSiglas("NEW");
        dto.setCodigoInterno("001");

        FormacionPoliticaEntity actualizado = new FormacionPoliticaEntity(20L, "Nuevo", "NEW", "001");

        when(repository.findById(20L)).thenReturn(Optional.of(existente));
        when(repository.save(any(FormacionPoliticaEntity.class))).thenReturn(actualizado);

        Optional<FormacionPolitica> resultado = service.actualizar(20L, dto);

        assertTrue(resultado.isPresent());
        assertEquals("Nuevo", resultado.get().getNombre());
        verify(repository).findById(20L);
        verify(repository).save(existente);
    }

    @Test
    void testActualizarNoExistente() {
        FormacionPolitica dto = new FormacionPolitica();
        dto.setNombre("Inexistente");

        when(repository.findById(999L)).thenReturn(Optional.empty());

        Optional<FormacionPolitica> resultado = service.actualizar(999L, dto);

        assertFalse(resultado.isPresent());
        verify(repository).findById(999L);
        verify(repository, never()).save(any());
    }

    @Test
    void testEliminarPorId() {
        doNothing().when(repository).deleteById(5L);

        service.eliminarPorId(5L);

        verify(repository).deleteById(5L);
    }
}
