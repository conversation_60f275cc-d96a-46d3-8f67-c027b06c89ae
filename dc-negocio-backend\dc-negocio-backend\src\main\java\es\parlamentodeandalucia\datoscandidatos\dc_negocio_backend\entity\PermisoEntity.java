package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity;

import jakarta.persistence.*;
import net.minidev.json.annotate.JsonIgnore;

@Entity
@Table(name = "dac_t_permiso")
public class PermisoEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "dac_id_permiso")
    private Long id;

    @Column(name = "dac_tx_nombre", nullable = false, length = 255)
    private String nombre;

    @Column(name = "dac_bo_activo", nullable = false)
    private Boolean activo;

    // Relación con Funcionalidad
    @ManyToOne
    @JsonIgnore
    @JoinColumn(name = "dac_id_funcionalidad", nullable = false)
    private FuncionalidadEntity funcionalidad;

    // Constructor por defecto
    public PermisoEntity() {
    }

    // Constructor completo
    public PermisoEntity(Long id, String nombre, Boolean activo, FuncionalidadEntity funcionalidad) {
        this.id = id;
        this.nombre = nombre;
        this.activo = activo;
        this.funcionalidad = funcionalidad;
    }

    // Getters y setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getNombre() {
        return nombre;
    }

    public void setNombre(String nombre) {
        this.nombre = nombre;
    }

    public Boolean getActivo() {
        return activo;
    }

    public void setActivo(Boolean activo) {
        this.activo = activo;
    }

    public FuncionalidadEntity getFuncionalidad() {
        return funcionalidad;
    }

    public void setFuncionalidad(FuncionalidadEntity funcionalidad) {
        this.funcionalidad = funcionalidad;
    }
}
