package es.parlamentodeandalucia.datoscandidatos.dto;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import es.parlamentodeandalucia.datoscandidatos.dto.Candidato;
import es.parlamentodeandalucia.datoscandidatos.dto.CandidaturaEstadoCandidatura;
import es.parlamentodeandalucia.datoscandidatos.dto.Circunscripcion;
import es.parlamentodeandalucia.datoscandidatos.dto.FormacionPolitica;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.springframework.format.annotation.DateTimeFormat;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import org.hibernate.validator.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import jakarta.annotation.Generated;

/**
 * Candidatura
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2025-08-18T11:59:39.046397400+02:00[Europe/Madrid]")
public class Candidatura {

  private Long id;

  private Integer orden;

  private String usuarioCreacion;

  @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
  private OffsetDateTime fechaCreacion;

  private String usuarioValidacion;

  @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
  private OffsetDateTime fechaValidacion;

  private String comentarioValidacion;

  private String observacionRechazo;

  private FormacionPolitica formacionPolitica;

  private Circunscripcion circunscripcion;

  private CandidaturaEstadoCandidatura estadoCandidatura;

  private CandidaturaEstadoCandidatura tipoCandidatura;

  @Valid
  private List<@Valid Candidato> candidatos;

  public Candidatura id(Long id) {
    this.id = id;
    return this;
  }

  /**
   * Get id
   * @return id
  */
  
  @Schema(name = "id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("id")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public Candidatura orden(Integer orden) {
    this.orden = orden;
    return this;
  }

  /**
   * Get orden
   * @return orden
  */
  
  @Schema(name = "orden", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("orden")
  public Integer getOrden() {
    return orden;
  }

  public void setOrden(Integer orden) {
    this.orden = orden;
  }

  public Candidatura usuarioCreacion(String usuarioCreacion) {
    this.usuarioCreacion = usuarioCreacion;
    return this;
  }

  /**
   * Get usuarioCreacion
   * @return usuarioCreacion
  */
  
  @Schema(name = "usuarioCreacion", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("usuarioCreacion")
  public String getUsuarioCreacion() {
    return usuarioCreacion;
  }

  public void setUsuarioCreacion(String usuarioCreacion) {
    this.usuarioCreacion = usuarioCreacion;
  }

  public Candidatura fechaCreacion(OffsetDateTime fechaCreacion) {
    this.fechaCreacion = fechaCreacion;
    return this;
  }

  /**
   * Get fechaCreacion
   * @return fechaCreacion
  */
  @Valid 
  @Schema(name = "fechaCreacion", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("fechaCreacion")
  public OffsetDateTime getFechaCreacion() {
    return fechaCreacion;
  }

  public void setFechaCreacion(OffsetDateTime fechaCreacion) {
    this.fechaCreacion = fechaCreacion;
  }

  public Candidatura usuarioValidacion(String usuarioValidacion) {
    this.usuarioValidacion = usuarioValidacion;
    return this;
  }

  /**
   * Get usuarioValidacion
   * @return usuarioValidacion
  */
  
  @Schema(name = "usuarioValidacion", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("usuarioValidacion")
  public String getUsuarioValidacion() {
    return usuarioValidacion;
  }

  public void setUsuarioValidacion(String usuarioValidacion) {
    this.usuarioValidacion = usuarioValidacion;
  }

  public Candidatura fechaValidacion(OffsetDateTime fechaValidacion) {
    this.fechaValidacion = fechaValidacion;
    return this;
  }

  /**
   * Get fechaValidacion
   * @return fechaValidacion
  */
  @Valid 
  @Schema(name = "fechaValidacion", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("fechaValidacion")
  public OffsetDateTime getFechaValidacion() {
    return fechaValidacion;
  }

  public void setFechaValidacion(OffsetDateTime fechaValidacion) {
    this.fechaValidacion = fechaValidacion;
  }

  public Candidatura comentarioValidacion(String comentarioValidacion) {
    this.comentarioValidacion = comentarioValidacion;
    return this;
  }

  /**
   * Get comentarioValidacion
   * @return comentarioValidacion
  */
  
  @Schema(name = "comentarioValidacion", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("comentarioValidacion")
  public String getComentarioValidacion() {
    return comentarioValidacion;
  }

  public void setComentarioValidacion(String comentarioValidacion) {
    this.comentarioValidacion = comentarioValidacion;
  }

  public Candidatura observacionRechazo(String observacionRechazo) {
    this.observacionRechazo = observacionRechazo;
    return this;
  }

  /**
   * Get observacionRechazo
   * @return observacionRechazo
  */
  
  @Schema(name = "observacionRechazo", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("observacionRechazo")
  public String getObservacionRechazo() {
    return observacionRechazo;
  }

  public void setObservacionRechazo(String observacionRechazo) {
    this.observacionRechazo = observacionRechazo;
  }

  public Candidatura formacionPolitica(FormacionPolitica formacionPolitica) {
    this.formacionPolitica = formacionPolitica;
    return this;
  }

  /**
   * Get formacionPolitica
   * @return formacionPolitica
  */
  @Valid 
  @Schema(name = "formacionPolitica", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("formacionPolitica")
  public FormacionPolitica getFormacionPolitica() {
    return formacionPolitica;
  }

  public void setFormacionPolitica(FormacionPolitica formacionPolitica) {
    this.formacionPolitica = formacionPolitica;
  }

  public Candidatura circunscripcion(Circunscripcion circunscripcion) {
    this.circunscripcion = circunscripcion;
    return this;
  }

  /**
   * Get circunscripcion
   * @return circunscripcion
  */
  @Valid 
  @Schema(name = "circunscripcion", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("circunscripcion")
  public Circunscripcion getCircunscripcion() {
    return circunscripcion;
  }

  public void setCircunscripcion(Circunscripcion circunscripcion) {
    this.circunscripcion = circunscripcion;
  }

  public Candidatura estadoCandidatura(CandidaturaEstadoCandidatura estadoCandidatura) {
    this.estadoCandidatura = estadoCandidatura;
    return this;
  }

  /**
   * Get estadoCandidatura
   * @return estadoCandidatura
  */
  @Valid 
  @Schema(name = "estadoCandidatura", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("estadoCandidatura")
  public CandidaturaEstadoCandidatura getEstadoCandidatura() {
    return estadoCandidatura;
  }

  public void setEstadoCandidatura(CandidaturaEstadoCandidatura estadoCandidatura) {
    this.estadoCandidatura = estadoCandidatura;
  }

  public Candidatura tipoCandidatura(CandidaturaEstadoCandidatura tipoCandidatura) {
    this.tipoCandidatura = tipoCandidatura;
    return this;
  }

  /**
   * Get tipoCandidatura
   * @return tipoCandidatura
  */
  @Valid 
  @Schema(name = "tipoCandidatura", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("tipoCandidatura")
  public CandidaturaEstadoCandidatura getTipoCandidatura() {
    return tipoCandidatura;
  }

  public void setTipoCandidatura(CandidaturaEstadoCandidatura tipoCandidatura) {
    this.tipoCandidatura = tipoCandidatura;
  }

  public Candidatura candidatos(List<@Valid Candidato> candidatos) {
    this.candidatos = candidatos;
    return this;
  }

  public Candidatura addCandidatosItem(Candidato candidatosItem) {
    if (this.candidatos == null) {
      this.candidatos = new ArrayList<>();
    }
    this.candidatos.add(candidatosItem);
    return this;
  }

  /**
   * Get candidatos
   * @return candidatos
  */
  @Valid 
  @Schema(name = "candidatos", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("candidatos")
  public List<@Valid Candidato> getCandidatos() {
    return candidatos;
  }

  public void setCandidatos(List<@Valid Candidato> candidatos) {
    this.candidatos = candidatos;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Candidatura candidatura = (Candidatura) o;
    return Objects.equals(this.id, candidatura.id) &&
        Objects.equals(this.orden, candidatura.orden) &&
        Objects.equals(this.usuarioCreacion, candidatura.usuarioCreacion) &&
        Objects.equals(this.fechaCreacion, candidatura.fechaCreacion) &&
        Objects.equals(this.usuarioValidacion, candidatura.usuarioValidacion) &&
        Objects.equals(this.fechaValidacion, candidatura.fechaValidacion) &&
        Objects.equals(this.comentarioValidacion, candidatura.comentarioValidacion) &&
        Objects.equals(this.observacionRechazo, candidatura.observacionRechazo) &&
        Objects.equals(this.formacionPolitica, candidatura.formacionPolitica) &&
        Objects.equals(this.circunscripcion, candidatura.circunscripcion) &&
        Objects.equals(this.estadoCandidatura, candidatura.estadoCandidatura) &&
        Objects.equals(this.tipoCandidatura, candidatura.tipoCandidatura) &&
        Objects.equals(this.candidatos, candidatura.candidatos);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, orden, usuarioCreacion, fechaCreacion, usuarioValidacion, fechaValidacion, comentarioValidacion, observacionRechazo, formacionPolitica, circunscripcion, estadoCandidatura, tipoCandidatura, candidatos);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Candidatura {\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    orden: ").append(toIndentedString(orden)).append("\n");
    sb.append("    usuarioCreacion: ").append(toIndentedString(usuarioCreacion)).append("\n");
    sb.append("    fechaCreacion: ").append(toIndentedString(fechaCreacion)).append("\n");
    sb.append("    usuarioValidacion: ").append(toIndentedString(usuarioValidacion)).append("\n");
    sb.append("    fechaValidacion: ").append(toIndentedString(fechaValidacion)).append("\n");
    sb.append("    comentarioValidacion: ").append(toIndentedString(comentarioValidacion)).append("\n");
    sb.append("    observacionRechazo: ").append(toIndentedString(observacionRechazo)).append("\n");
    sb.append("    formacionPolitica: ").append(toIndentedString(formacionPolitica)).append("\n");
    sb.append("    circunscripcion: ").append(toIndentedString(circunscripcion)).append("\n");
    sb.append("    estadoCandidatura: ").append(toIndentedString(estadoCandidatura)).append("\n");
    sb.append("    tipoCandidatura: ").append(toIndentedString(tipoCandidatura)).append("\n");
    sb.append("    candidatos: ").append(toIndentedString(candidatos)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

