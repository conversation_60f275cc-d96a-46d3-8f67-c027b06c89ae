import { Component, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { KeycloakService } from 'keycloak-angular';
import { ModalConfirmarLogoutComponent } from '../modal-confirmar-logout/modal-confirmar-logout.component';

interface MenuItem {
  label: string;
  route?: string;
  icon?: string;
}

@Component({
  selector: 'app-header',
  standalone: true,
  imports: [CommonModule, RouterModule, ModalConfirmarLogoutComponent],
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.scss']
})
export class HeaderComponent {
  mainMenu: MenuItem[] = [
    { label: 'Inicio', route: '/home' },
    { label: 'Candidaturas', route: '/candidaturas' }
  ];

  adminMenu: MenuItem[] = [
    { label: 'Usuarios',               route: '/admin/usuarios',           icon: 'vector.png' },
    { label: 'Representantes',         route: '/admin/representantes',     icon: 'vector.png' },
    { label: 'Formaciones políticas',  route: '/admin/formaciones-politicas',        icon: 'vector.png' },
    { label: 'Circunscripciones',      route: '/admin/circunscripciones',  icon: 'vector.png' },
    { label: 'Configurar convocatorias', route: '/admin/convocatorias',    icon: 'vector.png' },
    { label: 'Plantillas',             route: '/admin/plantillas',         icon: 'vector.png' },
    { label: 'Validación de candidaturas',             route: '/admin/validacion-candidaturas',         icon: 'vector.png' },
    { label: 'Reset base de datos',    route: '/admin/reset',         icon: 'vector.png' },
    { label: 'Convocatoria', route: '/admin/convocatoria', icon: 'vector.png' },
    { label: 'Gestion funcionalidades', route: '/admin/gestion-funcionalidades', icon: 'vector.png' },
    { label: 'Gestion permisos', route: '/admin/gestion-permisos', icon: 'vector.png' }
  ];

  userMenu: MenuItem[] = [
    { label: 'Mi perfil',      route: '/perfil',        icon: 'vector.png' },
    { label: 'Configuración',  route: '/configuracion', icon: 'vector.png' }
  ];

  showAdmin = false;
  showUser = false;
  modalLogoutVisible = false;

  constructor(private keycloak: KeycloakService) {}

  toggleAdmin(): void {
    this.showAdmin = !this.showAdmin;
  }

  toggleUser(): void {
    this.showUser = !this.showUser;
  }

  abrirModalLogout() {
    this.modalLogoutVisible = true;
  }

  cerrarModalLogout() {
    this.modalLogoutVisible = false;
  }

  logoutFrontend() {
    window.location.href = 'http://localhost:4200/';
  }

  logoutKeycloak() {
    this.keycloak.logout('http://localhost:4200/');
  }

  @HostListener('document:click', ['$event.target'])
  onClick(target: HTMLElement) {
    if (!target.closest('.dropdown')) {
      this.showAdmin = false;
      this.showUser = false;
    }
  }
}
