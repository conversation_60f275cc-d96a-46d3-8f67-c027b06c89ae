-- ========================================
-- SCRIPT SPRINT 1 - TABLAS PRINCIPALES DEL SISTEMA
-- ========================================

-- ========================================
-- TABLAS PRINCIPALES
-- ========================================

-- Tabla de convocatorias
CREATE TABLE IF NOT EXISTS dac_t_convocatoria (
    dac_id_convocatoria BIGSERIAL,
    dac_fh_convocatoria date,
    dac_fh_elecciones date,
    dac_fh_inicio_presentacion date,
    dac_fh_fin_presentacion date,
    dac_fh_publicacion_boja date,
    dac_fh_irregularidades date,
    dac_fh_inicio_subsanacion date,
    dac_fh_fin_subsanacion date,
    dac_fh_proclamacion date,
    dac_fh_publicacion_proclamada date,
    dac_fh_inicio_declaracion date,
    dac_fh_fin_declaracion date,
    dac_fh_inicio_publicacion_internet date,
    dac_fh_fin_publicacion_internet date,
    dac_fh_cancelacion date,
    dac_bf_logotipo bytea,
    PRIMARY KEY (dac_id_convocatoria)
);

-- Tabla de usuarios del sistema (diseño simplificado)
CREATE TABLE IF NOT EXISTS dac_t_usuario (
    dac_id_usuario BIGSERIAL NOT NULL,
    dac_fk_username VARCHAR(255) NOT NULL UNIQUE,  -- Referencia a sgi_username o UID del LDAP
    dac_fh_alta DATE,
    dac_fh_baja DATE,
    dac_fh_last_login DATE,
    dac_bl_activo BOOLEAN,
    PRIMARY KEY (dac_id_usuario)
);

-- Tabla de auditoría
CREATE TABLE IF NOT EXISTS dac_t_auditoria (
    dac_id_auditoria BIGSERIAL,
    dac_tx_tabla varchar(255),
    dac_tx_columna varchar(255),
    dac_tx_valor_anterior varchar(255),
    dac_tx_valor_nuevo varchar(255),
    dac_id_usuario int8,
    dac_fh_modificacion date,
    dac_id_referencia int8,
    PRIMARY KEY (dac_id_auditoria)
);

-- Tabla de auditoría específica para convocatorias
CREATE TABLE IF NOT EXISTS dac_t_audita_convoc (
    dac_id_audita_convoc BIGSERIAL,
    dac_fk_convocatoria int8,
    dac_fk_usuario int8,
    dac_tx_columna varchar(255),
    dac_tx_valor_anterior varchar(255),
    dac_tx_valor_nuevo varchar(255),
    dac_fh_modificacion date,
    dac_tx_motivo varchar(255),
    PRIMARY KEY (dac_id_audita_convoc)
);

-- ========================================
-- TABLAS DE CONFIGURACIÓN
-- ========================================

-- Tabla de tipos de documento
CREATE TABLE IF NOT EXISTS dac_t_tipo_documento (
    dac_id_tipo_documento BIGSERIAL,
    dac_tx_nombre varchar(255),
    PRIMARY KEY (dac_id_tipo_documento)
);

-- Tabla de plantillas de documentos
CREATE TABLE IF NOT EXISTS dac_t_plantilla (
    dac_id_plantilla BIGSERIAL,
    dac_tx_nombre varchar(255),
    dac_bf_contenido bytea,
    dac_fk_tipo_documento int8,
    dac_bl_activa bool,
    PRIMARY KEY (dac_id_plantilla)
);

-- Tabla de roles del sistema
CREATE TABLE IF NOT EXISTS dac_t_rol (
    dac_id_rol BIGSERIAL,
    dac_tx_valor varchar(50) UNIQUE,
    PRIMARY KEY (dac_id_rol)
);

-- ========================================
-- TABLAS DE GESTIÓN DE USUARIOS
-- ========================================

-- Tabla de reset de contraseñas
CREATE TABLE IF NOT EXISTS dac_t_reset (
    dac_id_reset BIGSERIAL,
    dac_fk_usuario int8,
    dac_fh_reset date,
    PRIMARY KEY (dac_id_reset)
);

-- Tabla de relación usuario-rol
CREATE TABLE IF NOT EXISTS dac_t_usuario_rol (
    dac_id_usuario_rol BIGSERIAL,
    dac_fk_usuario int8,
    dac_fk_rol int8,
    dac_bl_defecto bool,
    PRIMARY KEY (dac_id_usuario_rol)
);

-- ========================================
-- TABLAS DE PERMISOS Y FUNCIONALIDADES
-- ========================================

-- Tabla de funcionalidades del sistema
CREATE TABLE IF NOT EXISTS dac_t_funcionalidad (
    dac_id_funcionalidad BIGSERIAL,
    dac_tx_valor varchar(255) UNIQUE,
    PRIMARY KEY (dac_id_funcionalidad)
);

-- Tabla de permisos
CREATE TABLE IF NOT EXISTS dac_t_permiso (
    dac_id_permiso BIGSERIAL,
    dac_tx_valor varchar(255),
    dac_fk_funcionalidad int8,
    PRIMARY KEY (dac_id_permiso),
    UNIQUE (dac_tx_valor, dac_fk_funcionalidad)
);

-- Tabla de relación rol-permiso
CREATE TABLE IF NOT EXISTS dac_t_rol_permiso (
    dac_id_rol_permiso BIGSERIAL,
    dac_fk_rol int8,
    dac_fk_permiso int8,
    PRIMARY KEY (dac_id_rol_permiso)
);

-- ========================================
-- TABLAS DE DATOS ELECTORALES
-- ========================================

-- Tabla de circunscripciones
CREATE TABLE IF NOT EXISTS dac_t_circunscripcion (
    dac_id_circunscripcion BIGSERIAL,
    dac_tx_nombre varchar(255) UNIQUE,
    dac_tx_codigo varchar(50) UNIQUE,
    dac_bl_activa bool,
    PRIMARY KEY (dac_id_circunscripcion)
);

-- Tabla de formaciones políticas
CREATE TABLE IF NOT EXISTS dac_t_formacion_politica (
    dac_id_formacion BIGSERIAL,
    dac_tx_nombre varchar(255) UNIQUE,
    dac_tx_siglas varchar(50) UNIQUE,
    dac_tx_codigo_interno varchar(50),
    dac_bl_activa bool,
    PRIMARY KEY (dac_id_formacion)
);

-- ========================================
-- TABLAS DE CANDIDATURAS Y CANDIDATOS
-- ========================================

-- Tabla de estados de candidatura
CREATE TABLE IF NOT EXISTS dac_t_estado_candidatura (
    dac_id_estado_candidatura BIGSERIAL,
    dac_tx_valor varchar(50),
    PRIMARY KEY (dac_id_estado_candidatura)
);

-- Tabla de tipos de candidatura
CREATE TABLE IF NOT EXISTS dac_t_tipo_candidatura (
    dac_id_tipo_candidatura BIGSERIAL,
    dac_tx_valor varchar(50),
    PRIMARY KEY (dac_id_tipo_candidatura)
);

-- Tabla de estados de candidato
CREATE TABLE IF NOT EXISTS dac_t_estado_candidato (
    dac_id_estado_candidato BIGSERIAL,
    dac_tx_valor varchar(50),
    PRIMARY KEY (dac_id_estado_candidato)
);

-- Tabla de tipos de candidato
CREATE TABLE IF NOT EXISTS dac_t_tipo_candidato (
    dac_id_tipo_candidato BIGSERIAL,
    dac_tx_valor varchar(50),
    dac_tx_descripcion varchar(50),
    PRIMARY KEY (dac_id_tipo_candidato)
);

-- Tabla de declaraciones
CREATE TABLE IF NOT EXISTS dac_t_declaracion (
    dac_id_declaracion BIGSERIAL,
    PRIMARY KEY (dac_id_declaracion)
);

-- dac_t_candidatura - Candidaturas (relación entre candidatos y convocatorias)
CREATE TABLE IF NOT EXISTS dac_t_candidatura (
    dac_id_candidatura BIGSERIAL,
    dac_fk_formacion_politica int8 NOT NULL,
    dac_fk_circunscripcion bigint,
    dac_in_orden int8,
    dac_fk_estado_candidatura int8,
    dac_fk_tipo_candidatura int8,
    dac_tx_usuario_creacion varchar(255),
    dac_fh_creacion date DEFAULT CURRENT_DATE,
    dac_tx_usuario_validacion varchar(255),
    dac_fh_validacion date,
    dac_tx_comentario_validacion varchar(1024),
    dac_tx_observacion_rechazo varchar(1024),
    PRIMARY KEY (dac_id_candidatura)
);

-- Tabla de candidatos
CREATE TABLE IF NOT EXISTS dac_t_candidato (
    dac_id_candidato BIGSERIAL,
    dac_tx_nombre varchar(255),
    dac_tx_apellido1 varchar(255),
    dac_tx_apellido2 varchar(255),
    dac_fk_tipo_candidato int8,
    dac_in_orden int8,
    dac_fk_candidatura int8,
    dac_fk_estado_candidato int8,
    dac_tx_usuario_creacion varchar(255),
    dac_fh_creacion date,
    dac_tx_usuario_validacion varchar(255),
    dac_fh_validacion date,
    dac_tx_comentario_validacion varchar(1024),
    dac_tx_observacion_rechazo varchar(1024),
    dac_fk_declaracion int8,
    PRIMARY KEY (dac_id_candidato)
);

-- dac_t_importacion - Registro de importaciones realizadas
CREATE TABLE IF NOT EXISTS dac_t_importacion (
    dac_id_importacion BIGSERIAL,
    dac_tx_usuario_importacion varchar(255),
    dac_fh_importacion date DEFAULT CURRENT_DATE,
    dac_fk_nombre_fichero varchar(255),
    dac_tx_formato_fichero varchar(50),
    dac_bf_contenido bytea,
    dac_in_filas_correctas int8,
    dac_in_filas_incorrectas int8,
    dac_bf_resultado bytea,
    PRIMARY KEY (dac_id_importacion)
);

-- dac_t_importacion_candidatura - Relación entre importaciones y candidaturas creadas
CREATE TABLE IF NOT EXISTS dac_t_importacion_candidatura (
    dac_id_importacion_candidatura BIGSERIAL,
    dac_fk_candidatura int8 NOT NULL,
    dac_fk_importacion int8 NOT NULL,
    PRIMARY KEY (dac_id_importacion_candidatura)
);

-- dac_t_error_import - Errores específicos de importación
CREATE TABLE IF NOT EXISTS dac_t_error_import (
    dac_id_error_import BIGSERIAL,
    dac_fk_importacion int8 NOT NULL,
    dac_in_linea int8,
    dac_tx_campo varchar(255),
    dac_tx_error varchar(1024),
    PRIMARY KEY (dac_id_error_import)
);

-- ========================================
-- RESTRICCIONES DE CLAVE FORÁNEA
-- ========================================

-- Restricciones de tablas principales
-- NOTA: La FK a sgi_t_credencial se creará cuando ambas bases de datos estén conectadas

ALTER TABLE dac_t_audita_convoc ADD CONSTRAINT fk_dac_t_audita_convoc_dac_t_convocatoria
    FOREIGN KEY (dac_fk_convocatoria) REFERENCES dac_t_convocatoria(dac_id_convocatoria);

ALTER TABLE dac_t_audita_convoc ADD CONSTRAINT fk_dac_t_audita_convoc_dac_t_usuario
    FOREIGN KEY (dac_fk_usuario) REFERENCES dac_t_usuario(dac_id_usuario);

-- dac_t_reset - registros de reseteo de base de datos
CREATE TABLE IF NOT EXISTS dac_t_reset (
    dac_id_reset BIGSERIAL NOT NULL,
    dac_fk_usuario varchar(255),
    dac_fh_reset timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (dac_id_reset)
);

-- Restricciones de configuración
ALTER TABLE dac_t_plantilla ADD CONSTRAINT fk_dac_t_plantilla_dac_t_tipo_documento
    FOREIGN KEY (dac_fk_tipo_documento) REFERENCES dac_t_tipo_documento(dac_id_tipo_documento);

-- Restricciones de gestión de usuarios
ALTER TABLE dac_t_usuario_rol ADD CONSTRAINT fk_dac_t_usuario_rol_dac_t_usuario
    FOREIGN KEY (dac_fk_usuario) REFERENCES dac_t_usuario(dac_id_usuario);

ALTER TABLE dac_t_usuario_rol ADD CONSTRAINT fk_dac_t_usuario_rol_dac_t_rol
    FOREIGN KEY (dac_fk_rol) REFERENCES dac_t_rol(dac_id_rol);

-- Restricciones de permisos y funcionalidades
ALTER TABLE dac_t_permiso ADD CONSTRAINT fk_dac_t_permiso_dac_t_funcionalidad
    FOREIGN KEY (dac_fk_funcionalidad) REFERENCES dac_t_funcionalidad(dac_id_funcionalidad);

ALTER TABLE dac_t_rol_permiso ADD CONSTRAINT fk_dac_t_rol_permiso_dac_t_rol
    FOREIGN KEY (dac_fk_rol) REFERENCES dac_t_rol(dac_id_rol);

ALTER TABLE dac_t_rol_permiso ADD CONSTRAINT fk_dac_t_rol_permiso_dac_t_permiso
    FOREIGN KEY (dac_fk_permiso) REFERENCES dac_t_permiso(dac_id_permiso);

-- Restricciones de candidaturas
ALTER TABLE dac_t_candidatura ADD CONSTRAINT fk_dac_t_candidaturas_dac_t_formacion_politica
    FOREIGN KEY (dac_fk_formacion_politica) REFERENCES dac_t_formacion_politica(dac_id_formacion);

ALTER TABLE dac_t_candidatura ADD CONSTRAINT fk_dac_t_candidaturas_dac_t_circunscripcion
    FOREIGN KEY (dac_fk_circunscripcion) REFERENCES dac_t_circunscripcion(dac_id_circunscripcion);

ALTER TABLE dac_t_candidatura ADD CONSTRAINT fk_dac_t_candidaturas_dac_t_estado_candidatura
    FOREIGN KEY (dac_fk_estado_candidatura) REFERENCES dac_t_estado_candidatura(dac_id_estado_candidatura);

ALTER TABLE dac_t_candidatura ADD CONSTRAINT fk_dac_t_candidaturas_dac_t_tipo_candidatura
    FOREIGN KEY (dac_fk_tipo_candidatura) REFERENCES dac_t_tipo_candidatura(dac_id_tipo_candidatura);

-- Restricciones de candidatos
ALTER TABLE dac_t_candidato ADD CONSTRAINT fk_dac_t_candidato_dac_t_tipo_candidato
    FOREIGN KEY (dac_fk_tipo_candidato) REFERENCES dac_t_tipo_candidato(dac_id_tipo_candidato);

ALTER TABLE dac_t_candidato ADD CONSTRAINT fk_dac_t_candidato_dac_t_candidatura
    FOREIGN KEY (dac_fk_candidatura) REFERENCES dac_t_candidatura(dac_id_candidatura);

ALTER TABLE dac_t_candidato ADD CONSTRAINT fk_dac_t_candidato_dac_t_estado_candidato
    FOREIGN KEY (dac_fk_estado_candidato) REFERENCES dac_t_estado_candidato(dac_id_estado_candidato);

ALTER TABLE dac_t_candidato ADD CONSTRAINT fk_dac_t_candidato_dac_t_declaracion
    FOREIGN KEY (dac_fk_declaracion) REFERENCES dac_t_declaracion(dac_id_declaracion);

-- Restricciones de importación
ALTER TABLE dac_t_importacion_candidatura ADD CONSTRAINT fk_dac_t_importacion_candidatura_dac_t_candidatura
    FOREIGN KEY (dac_fk_candidatura) REFERENCES dac_t_candidatura(dac_id_candidatura);

ALTER TABLE dac_t_importacion_candidatura ADD CONSTRAINT fk_dac_t_importacion_candidatura_dac_t_importacion
    FOREIGN KEY (dac_fk_importacion) REFERENCES dac_t_importacion(dac_id_importacion);

ALTER TABLE dac_t_error_import ADD CONSTRAINT fk_dac_t_error_import_dac_t_importacion
    FOREIGN KEY (dac_fk_importacion) REFERENCES dac_t_importacion(dac_id_importacion);

-- ========================================
-- COMENTARIOS DE DOCUMENTACIÓN
-- ========================================
COMMENT ON TABLE dac_t_convocatoria IS 'Convocatorias electorales con todas sus fechas importantes';
COMMENT ON TABLE dac_t_usuario IS 'Usuarios del sistema - diseño simplificado con solo campos esenciales';
COMMENT ON TABLE dac_t_auditoria IS 'Tabla de auditoría general para cambios en las tablas CANDIDATURA y CANDIDATO';
COMMENT ON TABLE dac_t_audita_convoc IS 'Tabla de auditoría específica para cambios en convocatorias';
COMMENT ON TABLE dac_t_tipo_documento IS 'Catálogo de tipos de documentos del sistema';
COMMENT ON TABLE dac_t_plantilla IS 'Plantillas de documentos asociadas a tipos específicos';
COMMENT ON TABLE dac_t_rol IS 'Catálogo de roles del sistema (ADMINISTRADOR DEL SISTEMA, ADMINISTRADOR DE JEA, MIEMBRO DE JEA, REPRESENTANTE, CANDIDATO)';
COMMENT ON TABLE dac_t_reset IS 'Registro de resets de contraseña de usuarios';
COMMENT ON TABLE dac_t_usuario_rol IS 'Relación entre usuarios y roles del sistema';
COMMENT ON TABLE dac_t_funcionalidad IS 'Catálogo de funcionalidades del sistema (GESTIÓN DE USUARIOS, GESTIÓN DE ROLES, etc.)';
COMMENT ON TABLE dac_t_permiso IS 'Catálogo de permisos específicos asociados a funcionalidades (CREAR, CONSULTAR, MODIFICAR, ELIMINAR, etc.)';
COMMENT ON TABLE dac_t_rol_permiso IS 'Relación entre roles y permisos del sistema';
COMMENT ON TABLE dac_t_circunscripcion IS 'Catálogo de circunscripciones electorales (provincias andaluzas)';
COMMENT ON TABLE dac_t_formacion_politica IS 'Catálogo de formaciones políticas participantes';
COMMENT ON TABLE dac_t_candidatura IS 'Candidaturas presentadas por formaciones políticas en circunscripciones';
COMMENT ON TABLE dac_t_estado_candidatura IS 'Catálogo de estados de candidatura (IMPORTADA, BORRADOR, VALIDADA, RECHAZADA)';
COMMENT ON TABLE dac_t_tipo_candidatura IS 'Catálogo de tipos de candidatura (IMPORTADA, MANUAL)';
COMMENT ON TABLE dac_t_candidato IS 'Candidatos individuales asociados a candidaturas';
COMMENT ON TABLE dac_t_estado_candidato IS 'Catálogo de estados de candidato (PROCLAMADO, VALIDADO, RECHAZADO)';
COMMENT ON TABLE dac_t_tipo_candidato IS 'Catálogo de tipos de candidato (TITULAR, SUPLENTE)';
COMMENT ON TABLE dac_t_declaracion IS 'Declaraciones de candidatos';
COMMENT ON TABLE dac_t_importacion IS 'Registro de importaciones de archivos CSV realizadas';
COMMENT ON TABLE dac_t_importacion_candidatura IS 'Relación entre importaciones y candidaturas creadas';
COMMENT ON TABLE dac_t_error_import IS 'Errores específicos ocurridos durante importaciones';

-- Comentarios para columnas de dac_t_importacion
COMMENT ON COLUMN dac_t_importacion.dac_id_importacion IS 'Identificador único de la importación';
COMMENT ON COLUMN dac_t_importacion.dac_tx_usuario_importacion IS 'Usuario que realizó la importación';
COMMENT ON COLUMN dac_t_importacion.dac_fh_importacion IS 'Fecha de la importación';
COMMENT ON COLUMN dac_t_importacion.dac_fk_nombre_fichero IS 'Nombre del fichero importado';
COMMENT ON COLUMN dac_t_importacion.dac_tx_formato_fichero IS 'Formato del fichero importado (CSV, JSON, XML)';
COMMENT ON COLUMN dac_t_importacion.dac_bf_contenido IS 'Contenido del fichero importado';
COMMENT ON COLUMN dac_t_importacion.dac_in_filas_correctas IS 'Número de filas procesadas correctamente';
COMMENT ON COLUMN dac_t_importacion.dac_in_filas_incorrectas IS 'Número de filas con errores';
COMMENT ON COLUMN dac_t_importacion.dac_bf_resultado IS 'Resultado detallado de la importación';

-- Comentarios para campos específicos
COMMENT ON COLUMN dac_t_auditoria.dac_tx_tabla IS 'Nombre de la tabla modificada';
COMMENT ON COLUMN dac_t_auditoria.dac_tx_columna IS 'Columna de la tabla modificada';
COMMENT ON COLUMN dac_t_auditoria.dac_tx_valor_anterior IS 'Valor anterior del campo';
COMMENT ON COLUMN dac_t_auditoria.dac_tx_valor_nuevo IS 'Valor nuevo del campo';
COMMENT ON COLUMN dac_t_auditoria.dac_id_usuario IS 'Usuario que realizó la modificación';
COMMENT ON COLUMN dac_t_auditoria.dac_fh_modificacion IS 'Fecha de la modificación';
COMMENT ON COLUMN dac_t_auditoria.dac_id_referencia IS 'Identificador de referencia al registro';



COMMENT ON COLUMN dac_t_audita_convoc.dac_fk_convocatoria IS 'Referencia a la convocatoria modificada';
COMMENT ON COLUMN dac_t_audita_convoc.dac_fk_usuario IS 'Usuario que realizó la modificación';
COMMENT ON COLUMN dac_t_audita_convoc.dac_tx_columna IS 'Columna modificada';
COMMENT ON COLUMN dac_t_audita_convoc.dac_tx_valor_anterior IS 'Valor anterior';
COMMENT ON COLUMN dac_t_audita_convoc.dac_tx_valor_nuevo IS 'Valor nuevo';
COMMENT ON COLUMN dac_t_audita_convoc.dac_fh_modificacion IS 'Fecha de modificación';
COMMENT ON COLUMN dac_t_audita_convoc.dac_tx_motivo IS 'Motivo del cambio';

COMMENT ON COLUMN dac_t_reset.dac_fk_usuario IS 'Usuario que solicita el reset de contraseña';
COMMENT ON COLUMN dac_t_reset.dac_fh_reset IS 'Fecha del reset de contraseña';

COMMENT ON COLUMN dac_t_usuario_rol.dac_fk_usuario IS 'Usuario al que se asigna el rol';
COMMENT ON COLUMN dac_t_usuario_rol.dac_fk_rol IS 'Rol asignado al usuario';
COMMENT ON COLUMN dac_t_usuario_rol.dac_bl_defecto IS 'Indica si es el rol por defecto del usuario';

COMMENT ON COLUMN dac_t_rol.dac_tx_valor IS 'Nombre del rol (ADMINISTRADOR DEL SISTEMA, ADMINISTRADOR DE JEA, MIEMBRO DE JEA, REPRESENTANTE, CANDIDATO)';

COMMENT ON COLUMN dac_t_funcionalidad.dac_tx_valor IS 'Nombre de la funcionalidad del sistema';

COMMENT ON COLUMN dac_t_permiso.dac_tx_valor IS 'Nombre del permiso (CREAR, CONSULTAR, MODIFICAR, ELIMINAR, ACTIVAR, DESACTIVAR, EXPORTAR, REGISTRAR)';
COMMENT ON COLUMN dac_t_permiso.dac_fk_funcionalidad IS 'Funcionalidad a la que pertenece el permiso';

COMMENT ON COLUMN dac_t_rol_permiso.dac_fk_rol IS 'Rol al que se asigna el permiso';
COMMENT ON COLUMN dac_t_rol_permiso.dac_fk_permiso IS 'Permiso asignado al rol';

COMMENT ON COLUMN dac_t_circunscripcion.dac_tx_nombre IS 'Nombre de la circunscripción electoral';
COMMENT ON COLUMN dac_t_circunscripcion.dac_tx_codigo IS 'Código único de la circunscripción';
COMMENT ON COLUMN dac_t_circunscripcion.dac_bl_activa IS 'Indica si la circunscripción está activa';

COMMENT ON COLUMN dac_t_formacion_politica.dac_tx_nombre IS 'Nombre completo de la formación política';
COMMENT ON COLUMN dac_t_formacion_politica.dac_tx_siglas IS 'Siglas de la formación política';
COMMENT ON COLUMN dac_t_formacion_politica.dac_tx_codigo_interno IS 'Código interno de la formación política';
COMMENT ON COLUMN dac_t_formacion_politica.dac_bl_activa IS 'Indica si la formación política está activa';
CREATE INDEX IF NOT EXISTS idx_reset_usuario ON dac_t_reset(dac_fk_usuario);

-- Comentarios para tablas que existen en el script
COMMENT ON TABLE dac_t_candidatura IS 'Candidaturas de formaciones políticas por circunscripción';
COMMENT ON TABLE dac_t_estado_candidatura IS 'Estados de candidatura (importada, manual, validada por JEA, validada por JEP, rechazada)';
COMMENT ON TABLE dac_t_tipo_candidatura IS 'Tipos de candidatura (importada, manual)';
COMMENT ON TABLE dac_t_tipo_candidato IS 'Tipos de candidato (titular, suplente)';
COMMENT ON TABLE dac_t_estado_candidato IS 'Estados de candidato (importado, manual, validado)';
COMMENT ON TABLE dac_t_declaracion IS 'Declaraciones de candidatos';

COMMENT ON COLUMN dac_t_candidatura.dac_fk_formacion_politica IS 'Formación política que presenta la candidatura';
COMMENT ON COLUMN dac_t_candidatura.dac_fk_circunscripcion IS 'Circunscripción electoral donde se presenta';
COMMENT ON COLUMN dac_t_candidatura.dac_in_orden IS 'Orden de la candidatura dentro de la formación';
COMMENT ON COLUMN dac_t_candidatura.dac_fk_estado_candidatura IS 'Estado actual de la candidatura';
COMMENT ON COLUMN dac_t_candidatura.dac_fk_tipo_candidatura IS 'Tipo de candidatura';
COMMENT ON COLUMN dac_t_candidatura.dac_tx_usuario_creacion IS 'Usuario que creó la candidatura';
COMMENT ON COLUMN dac_t_candidatura.dac_fh_creacion IS 'Fecha de creación de la candidatura';
COMMENT ON COLUMN dac_t_candidatura.dac_tx_usuario_validacion IS 'Usuario que validó la candidatura';
COMMENT ON COLUMN dac_t_candidatura.dac_fh_validacion IS 'Fecha de validación de la candidatura';
COMMENT ON COLUMN dac_t_candidatura.dac_tx_comentario_validacion IS 'Comentarios de la validación';
COMMENT ON COLUMN dac_t_candidatura.dac_tx_observacion_rechazo IS 'Observaciones en caso de rechazo';

COMMENT ON COLUMN dac_t_estado_candidatura.dac_tx_valor IS 'Nombre del estado de candidatura';

COMMENT ON COLUMN dac_t_tipo_candidatura.dac_tx_valor IS 'Nombre del tipo de candidatura';

-- Índices para mejorar rendimiento
CREATE INDEX IF NOT EXISTS idx_auditoria_tabla ON dac_t_auditoria(dac_tx_tabla);
CREATE INDEX IF NOT EXISTS idx_auditoria_usuario ON dac_t_auditoria(dac_id_usuario);
CREATE INDEX IF NOT EXISTS idx_auditoria_fecha ON dac_t_auditoria(dac_fh_modificacion);
CREATE INDEX IF NOT EXISTS idx_auditoria_referencia ON dac_t_auditoria(dac_id_referencia);

CREATE INDEX IF NOT EXISTS idx_audita_convoc_convocatoria ON dac_t_audita_convoc(dac_fk_convocatoria);
CREATE INDEX IF NOT EXISTS idx_audita_convoc_usuario ON dac_t_audita_convoc(dac_fk_usuario);
CREATE INDEX IF NOT EXISTS idx_audita_convoc_fecha ON dac_t_audita_convoc(dac_fh_modificacion);

CREATE INDEX IF NOT EXISTS idx_usuario_username ON dac_t_usuario(dac_fk_username);
CREATE INDEX IF NOT EXISTS idx_usuario_activo ON dac_t_usuario(dac_bl_activo);

CREATE INDEX IF NOT EXISTS idx_plantilla_tipo_documento ON dac_t_plantilla(dac_fk_tipo_documento);
CREATE INDEX IF NOT EXISTS idx_plantilla_activa ON dac_t_plantilla(dac_bl_activa);

CREATE INDEX IF NOT EXISTS idx_reset_usuario ON dac_t_reset(dac_fk_usuario);
CREATE INDEX IF NOT EXISTS idx_reset_fecha ON dac_t_reset(dac_fh_reset);

CREATE INDEX IF NOT EXISTS idx_usuario_rol_usuario ON dac_t_usuario_rol(dac_fk_usuario);
CREATE INDEX IF NOT EXISTS idx_usuario_rol_rol ON dac_t_usuario_rol(dac_fk_rol);
CREATE INDEX IF NOT EXISTS idx_usuario_rol_defecto ON dac_t_usuario_rol(dac_bl_defecto);

CREATE INDEX IF NOT EXISTS idx_rol_valor ON dac_t_rol(dac_tx_valor);

CREATE INDEX IF NOT EXISTS idx_funcionalidad_valor ON dac_t_funcionalidad(dac_tx_valor);

CREATE INDEX IF NOT EXISTS idx_permiso_valor ON dac_t_permiso(dac_tx_valor);
CREATE INDEX IF NOT EXISTS idx_permiso_funcionalidad ON dac_t_permiso(dac_fk_funcionalidad);

CREATE INDEX IF NOT EXISTS idx_rol_permiso_rol ON dac_t_rol_permiso(dac_fk_rol);
CREATE INDEX IF NOT EXISTS idx_rol_permiso_permiso ON dac_t_rol_permiso(dac_fk_permiso);

CREATE INDEX IF NOT EXISTS idx_circunscripcion_nombre ON dac_t_circunscripcion(dac_tx_nombre);
CREATE INDEX IF NOT EXISTS idx_circunscripcion_codigo ON dac_t_circunscripcion(dac_tx_codigo);
CREATE INDEX IF NOT EXISTS idx_circunscripcion_activa ON dac_t_circunscripcion(dac_bl_activa);

CREATE INDEX IF NOT EXISTS idx_formacion_politica_nombre ON dac_t_formacion_politica(dac_tx_nombre);
CREATE INDEX IF NOT EXISTS idx_formacion_politica_siglas ON dac_t_formacion_politica(dac_tx_siglas);
CREATE INDEX IF NOT EXISTS idx_formacion_politica_activa ON dac_t_formacion_politica(dac_bl_activa);

CREATE INDEX IF NOT EXISTS idx_candidatura_formacion ON dac_t_candidatura(dac_fk_formacion_politica);
CREATE INDEX IF NOT EXISTS idx_candidatura_circunscripcion ON dac_t_candidatura(dac_fk_circunscripcion);
CREATE INDEX IF NOT EXISTS idx_candidatura_orden ON dac_t_candidatura(dac_in_orden);
CREATE INDEX IF NOT EXISTS idx_candidatura_estado ON dac_t_candidatura(dac_fk_estado_candidatura);
CREATE INDEX IF NOT EXISTS idx_candidatura_tipo ON dac_t_candidatura(dac_fk_tipo_candidatura);
CREATE INDEX IF NOT EXISTS idx_candidatura_creacion ON dac_t_candidatura(dac_fh_creacion);
CREATE INDEX IF NOT EXISTS idx_candidatura_validacion ON dac_t_candidatura(dac_fh_validacion);

CREATE INDEX IF NOT EXISTS idx_estado_candidatura_valor ON dac_t_estado_candidatura(dac_tx_valor);

CREATE INDEX IF NOT EXISTS idx_tipo_candidatura_valor ON dac_t_tipo_candidatura(dac_tx_valor);

-- Índices para tablas de importación
CREATE INDEX IF NOT EXISTS idx_importacion_usuario ON dac_t_importacion(dac_tx_usuario_importacion);
CREATE INDEX IF NOT EXISTS idx_importacion_fecha ON dac_t_importacion(dac_fh_importacion);
CREATE INDEX IF NOT EXISTS idx_importacion_filas ON dac_t_importacion(dac_in_filas_correctas, dac_in_filas_incorrectas);

CREATE INDEX IF NOT EXISTS idx_importacion_candidatura_importacion ON dac_t_importacion_candidatura(dac_fk_importacion);
CREATE INDEX IF NOT EXISTS idx_importacion_candidatura_candidatura ON dac_t_importacion_candidatura(dac_fk_candidatura);

CREATE INDEX IF NOT EXISTS idx_error_import_importacion ON dac_t_error_import(dac_fk_importacion);
CREATE INDEX IF NOT EXISTS idx_error_import_linea ON dac_t_error_import(dac_in_linea);

-- ========================================
-- DATOS INICIALES
-- ========================================

-- Datos iniciales para dac_t_tipo_documento
-- Los tipos de plantillas serán: Declaración Individual, BOPA, BOJA, Publicación Web
INSERT INTO dac_t_tipo_documento (dac_id_tipo_documento, dac_tx_nombre) VALUES
(1, 'Declaración Individual'),
(2, 'BOPA'),
(3, 'BOJA'),
(4, 'Publicación Web')
ON CONFLICT (dac_id_tipo_documento) DO NOTHING;

-- Datos iniciales para dac_t_plantilla
INSERT INTO dac_t_plantilla (dac_id_plantilla, dac_tx_nombre, dac_fk_tipo_documento, dac_bl_activa) VALUES
(1, 'Plantilla Declaración Individual v1.0', 1, true),
(2, 'Plantilla BOPA Estándar', 2, true),
(3, 'Plantilla BOJA Oficial', 3, true),
(4, 'Plantilla Web Pública', 4, true)
ON CONFLICT (dac_id_plantilla) DO NOTHING;

-- Datos iniciales para dac_t_rol
-- Los roles pueden ser: ADMINISTRADOR DEL SISTEMA, ADMINISTRADOR DE JEA, MIEMBRO DE JEA, REPRESENTANTE, CANDIDATO
INSERT INTO dac_t_rol (dac_id_rol, dac_tx_valor) VALUES
(1, 'ADMINISTRADOR DEL SISTEMA'),
(2, 'ADMINISTRADOR DE JEA'),
(3, 'MIEMBRO DE JEA'),
(4, 'REPRESENTANTE'),
(5, 'CANDIDATO')
ON CONFLICT (dac_id_rol) DO NOTHING;

-- Datos iniciales para dac_t_funcionalidad
-- Las funcionalidades pueden ser: GESTIÓN DE USUARIOS, GESTIÓN DE ROLES, GESTIÓN DE PERMISOS, etc.
INSERT INTO dac_t_funcionalidad (dac_id_funcionalidad, dac_tx_valor) VALUES
(1, 'GESTIÓN DE USUARIOS'),
(2, 'GESTIÓN DE ROLES'),
(3, 'GESTIÓN DE PERMISOS'),
(4, 'GESTIÓN DE FUNCIONALIDADES'),
(5, 'IMPORTACIÓN DATOS'),
(6, 'EXPORTACIÓN DATOS'),
(7, 'GESTIÓN DE CONVOCATORIAS'),
(8, 'GESTIÓN DE REPRESENTANTES'),
(9, 'GESTIÓN DE CANDIDATURAS'),
(10, 'GESTIÓN DE FORMACIONES POLÍTICAS'),
(11, 'GESTIÓN DE CIRCUNSCRIPCIONES'),
(12, 'GESTIÓN DE DECLARACIONES')
ON CONFLICT (dac_id_funcionalidad) DO NOTHING;

-- Datos iniciales para dac_t_permiso
-- Los permisos pueden ser: CREAR, CONSULTAR, MODIFICAR, ELIMINAR, ACTIVAR, DESACTIVAR, EXPORTAR, REGISTRAR
INSERT INTO dac_t_permiso (dac_id_permiso, dac_tx_valor, dac_fk_funcionalidad) VALUES
-- Permisos para GESTIÓN DE USUARIOS
(1, 'CREAR', 1),
(2, 'CONSULTAR', 1),
(3, 'MODIFICAR', 1),
(4, 'ELIMINAR', 1),
(5, 'ACTIVAR', 1),
(6, 'DESACTIVAR', 1),
-- Permisos para GESTIÓN DE ROLES
(7, 'CREAR', 2),
(8, 'CONSULTAR', 2),
(9, 'MODIFICAR', 2),
(10, 'ELIMINAR', 2),
-- Permisos para GESTIÓN DE PERMISOS
(11, 'CREAR', 3),
(12, 'CONSULTAR', 3),
(13, 'MODIFICAR', 3),
(14, 'ELIMINAR', 3),
-- Permisos para GESTIÓN DE FUNCIONALIDADES
(15, 'CREAR', 4),
(16, 'CONSULTAR', 4),
(17, 'MODIFICAR', 4),
(18, 'ELIMINAR', 4),
-- Permisos para IMPORTACIÓN DATOS
(19, 'REGISTRAR', 5),
-- Permisos para EXPORTACIÓN DATOS
(20, 'EXPORTAR', 6),
-- Permisos para GESTIÓN DE CONVOCATORIAS
(21, 'CREAR', 7),
(22, 'CONSULTAR', 7),
(23, 'MODIFICAR', 7),
(24, 'ELIMINAR', 7),
-- Permisos para GESTIÓN DE REPRESENTANTES
(25, 'CREAR', 8),
(26, 'CONSULTAR', 8),
(27, 'MODIFICAR', 8),
(28, 'ELIMINAR', 8),
-- Permisos para GESTIÓN DE CANDIDATURAS
(29, 'CREAR', 9),
(30, 'CONSULTAR', 9),
(31, 'MODIFICAR', 9),
(32, 'ELIMINAR', 9),
-- Permisos para GESTIÓN DE FORMACIONES POLÍTICAS
(33, 'CREAR', 10),
(34, 'CONSULTAR', 10),
(35, 'MODIFICAR', 10),
(36, 'ELIMINAR', 10),
-- Permisos para GESTIÓN DE CIRCUNSCRIPCIONES
(37, 'CREAR', 11),
(38, 'CONSULTAR', 11),
(39, 'MODIFICAR', 11),
(40, 'ELIMINAR', 11),
-- Permisos para GESTIÓN DE DECLARACIONES
(41, 'CREAR', 12),
(42, 'CONSULTAR', 12),
(43, 'MODIFICAR', 12),
(44, 'ELIMINAR', 12),
(45, 'REGISTRAR', 12)
ON CONFLICT (dac_id_permiso) DO NOTHING;

-- Datos iniciales para dac_t_circunscripcion
-- Provincias andaluzas según la imagen
INSERT INTO dac_t_circunscripcion (dac_id_circunscripcion, dac_tx_nombre, dac_tx_codigo, dac_bl_activa) VALUES
(11, 'ALMERÍA', '11', true),
(14, 'CÓRDOBA', '14', true),
(18, 'GRANADA', '18', true),
(21, 'HUELVA', '21', true),
(23, 'JAÉN', '23', true),
(29, 'MÁLAGA', '29', true),
(41, 'SEVILLA', '41', true),
(4, 'CÁDIZ', '4', true)
ON CONFLICT (dac_id_circunscripcion) DO NOTHING;

-- Datos iniciales para dac_t_estado_candidatura
-- Los estados pueden ser: PROCLAMADA, VALIDADA, RECHAZADA
INSERT INTO dac_t_estado_candidatura (dac_id_estado_candidatura, dac_tx_valor) VALUES
(1, 'PROCLAMADA'),
(2, 'VALIDADA'),
(3, 'RECHAZADA')
ON CONFLICT (dac_id_estado_candidatura) DO NOTHING;

-- Datos iniciales para dac_t_tipo_candidatura
-- Los tipos pueden ser: IMPORTADA, MANUAL
INSERT INTO dac_t_tipo_candidatura (dac_id_tipo_candidatura, dac_tx_valor) VALUES
(1, 'IMPORTADA'),
(2, 'MANUAL')
ON CONFLICT (dac_id_tipo_candidatura) DO NOTHING;

-- Datos iniciales para dac_t_tipo_candidato
-- Los tipos pueden ser: TITULAR, SUPLENTE
INSERT INTO dac_t_tipo_candidato (dac_id_tipo_candidato, dac_tx_valor, dac_tx_descripcion) VALUES
(1, 'TITULAR', 'Candidato titular'),
(2, 'SUPLENTE', 'Candidato suplente')
ON CONFLICT (dac_id_tipo_candidato) DO NOTHING;

-- Datos iniciales para dac_t_formacion_politica
-- Códigos internos según CSV real
INSERT INTO dac_t_formacion_politica (dac_id_formacion, dac_tx_nombre, dac_tx_siglas, dac_tx_codigo_interno, dac_bl_activa) VALUES
(1, 'PARTIDO POPULAR', 'PP', '1', true),
(2, 'PARTIDO SOCIALISTA', 'PSOE', '2', true),
(3, 'VOX', 'VOX', '3', true),
(4, 'POR ANDALUCIA', 'PAND', '4', true),
(5, 'Ciudadanos - Partido de la Ciudadanía', 'Cs', '5', true),
(6, 'Izquierda Unida', 'IU', '6', true),
(7, 'Adelante Andalucía', 'AA', '7', true)
ON CONFLICT (dac_id_formacion) DO NOTHING;

-- Datos iniciales para dac_t_estado_candidato
-- Los estados pueden ser: PROCLAMADO, VALIDADO, RECHAZADO
INSERT INTO dac_t_estado_candidato (dac_id_estado_candidato, dac_tx_valor) VALUES
(1, 'PROCLAMADO'),
(2, 'VALIDADO'),
(3, 'RECHAZADO')
ON CONFLICT (dac_id_estado_candidato) DO NOTHING;

-- Datos iniciales para dac_t_tipo_candidato
-- Los tipos pueden ser: TITULAR, SUPLENTE
INSERT INTO dac_t_tipo_candidato (dac_id_tipo_candidato, dac_tx_valor, dac_tx_descripcion) VALUES
(1, 'TITULAR', 'Candidato titular'),
(2, 'SUPLENTE', 'Candidato suplente')
ON CONFLICT (dac_id_tipo_candidato) DO NOTHING;

-- Datos iniciales para dac_t_declaracion
-- Declaraciones básicas de ejemplo
INSERT INTO dac_t_declaracion (dac_id_declaracion) VALUES
(1),
(2),
(3)
ON CONFLICT (dac_id_declaracion) DO NOTHING;

-- ========================================
-- TRIGGERS DE AUDITORÍA
-- ========================================

-- Función para establecer el contexto del usuario actual
-- Esta función debe ser llamada desde la aplicación antes de realizar cambios
CREATE OR REPLACE FUNCTION set_current_user_context(p_user_id int8)
RETURNS void AS $$
BEGIN
    -- Establecer el ID del usuario en una variable de sesión
    PERFORM set_config('app.current_user_id', p_user_id::text, false);
END;
$$ LANGUAGE plpgsql;

-- Función para obtener el ID del usuario actual desde el contexto
CREATE OR REPLACE FUNCTION get_current_user_id()
RETURNS int8 AS $$
DECLARE
    v_user_id text;
BEGIN
    -- Obtener el ID del usuario desde la variable de sesión
    v_user_id := current_setting('app.current_user_id', true);

    -- Si no hay contexto establecido, retornar NULL
    IF v_user_id IS NULL OR v_user_id = '' THEN
        RETURN NULL;
    END IF;

    RETURN v_user_id::int8;
EXCEPTION
    WHEN OTHERS THEN
        RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Función genérica para auditoría de cambios
CREATE OR REPLACE FUNCTION fn_auditoria_cambios()
RETURNS TRIGGER AS $$
DECLARE
    v_usuario_id int8;
    v_tabla_nombre varchar(255);
    v_columna varchar(255);
    v_valor_anterior varchar(255);
    v_valor_nuevo varchar(255);
    v_id_referencia int8;
    rec_old record;
    rec_new record;
BEGIN
    -- Obtener el nombre de la tabla
    v_tabla_nombre := TG_TABLE_NAME;

    -- Obtener el ID de referencia según la tabla
    IF TG_TABLE_NAME = 'dac_t_candidatura' THEN
        IF TG_OP = 'DELETE' THEN
            v_id_referencia := OLD.dac_id_candidatura;
        ELSE
            v_id_referencia := NEW.dac_id_candidatura;
        END IF;
    ELSIF TG_TABLE_NAME = 'dac_t_candidato' THEN
        IF TG_OP = 'DELETE' THEN
            v_id_referencia := OLD.dac_id_candidato;
        ELSE
            v_id_referencia := NEW.dac_id_candidato;
        END IF;
    END IF;

    -- Obtener el ID del usuario actual desde el contexto de la aplicación
    v_usuario_id := get_current_user_id();

    -- Procesar según el tipo de operación
    IF TG_OP = 'UPDATE' THEN
        rec_old := OLD;
        rec_new := NEW;

        -- Auditar cambios en dac_t_candidatura
        IF TG_TABLE_NAME = 'dac_t_candidatura' THEN
            -- Auditar cambio en dac_fk_formacion_politica
            IF OLD.dac_fk_formacion_politica IS DISTINCT FROM NEW.dac_fk_formacion_politica THEN
                INSERT INTO dac_t_auditoria (dac_tx_tabla, dac_tx_columna, dac_tx_valor_anterior, dac_tx_valor_nuevo, dac_id_usuario, dac_fh_modificacion, dac_id_referencia)
                VALUES (v_tabla_nombre, 'dac_fk_formacion_politica', COALESCE(OLD.dac_fk_formacion_politica::varchar, ''), COALESCE(NEW.dac_fk_formacion_politica::varchar, ''), v_usuario_id, CURRENT_DATE, v_id_referencia);
            END IF;

            -- Auditar cambio en dac_fk_circunscripcion
            IF OLD.dac_fk_circunscripcion IS DISTINCT FROM NEW.dac_fk_circunscripcion THEN
                INSERT INTO dac_t_auditoria (dac_tx_tabla, dac_tx_columna, dac_tx_valor_anterior, dac_tx_valor_nuevo, dac_id_usuario, dac_fh_modificacion, dac_id_referencia)
                VALUES (v_tabla_nombre, 'dac_fk_circunscripcion', COALESCE(OLD.dac_fk_circunscripcion::varchar, ''), COALESCE(NEW.dac_fk_circunscripcion::varchar, ''), v_usuario_id, CURRENT_DATE, v_id_referencia);
            END IF;

            -- Auditar cambio en dac_in_orden
            IF OLD.dac_in_orden IS DISTINCT FROM NEW.dac_in_orden THEN
                INSERT INTO dac_t_auditoria (dac_tx_tabla, dac_tx_columna, dac_tx_valor_anterior, dac_tx_valor_nuevo, dac_id_usuario, dac_fh_modificacion, dac_id_referencia)
                VALUES (v_tabla_nombre, 'dac_in_orden', COALESCE(OLD.dac_in_orden::varchar, ''), COALESCE(NEW.dac_in_orden::varchar, ''), v_usuario_id, CURRENT_DATE, v_id_referencia);
            END IF;

            -- Auditar cambio en dac_fk_estado_candidatura
            IF OLD.dac_fk_estado_candidatura IS DISTINCT FROM NEW.dac_fk_estado_candidatura THEN
                INSERT INTO dac_t_auditoria (dac_tx_tabla, dac_tx_columna, dac_tx_valor_anterior, dac_tx_valor_nuevo, dac_id_usuario, dac_fh_modificacion, dac_id_referencia)
                VALUES (v_tabla_nombre, 'dac_fk_estado_candidatura', COALESCE(OLD.dac_fk_estado_candidatura::varchar, ''), COALESCE(NEW.dac_fk_estado_candidatura::varchar, ''), v_usuario_id, CURRENT_DATE, v_id_referencia);
            END IF;

            -- Auditar cambio en dac_fk_tipo_candidatura
            IF OLD.dac_fk_tipo_candidatura IS DISTINCT FROM NEW.dac_fk_tipo_candidatura THEN
                INSERT INTO dac_t_auditoria (dac_tx_tabla, dac_tx_columna, dac_tx_valor_anterior, dac_tx_valor_nuevo, dac_id_usuario, dac_fh_modificacion, dac_id_referencia)
                VALUES (v_tabla_nombre, 'dac_fk_tipo_candidatura', COALESCE(OLD.dac_fk_tipo_candidatura::varchar, ''), COALESCE(NEW.dac_fk_tipo_candidatura::varchar, ''), v_usuario_id, CURRENT_DATE, v_id_referencia);
            END IF;

            -- Auditar cambio en dac_tx_usuario_validacion
            IF OLD.dac_tx_usuario_validacion IS DISTINCT FROM NEW.dac_tx_usuario_validacion THEN
                INSERT INTO dac_t_auditoria (dac_tx_tabla, dac_tx_columna, dac_tx_valor_anterior, dac_tx_valor_nuevo, dac_id_usuario, dac_fh_modificacion, dac_id_referencia)
                VALUES (v_tabla_nombre, 'dac_tx_usuario_validacion', COALESCE(OLD.dac_tx_usuario_validacion, ''), COALESCE(NEW.dac_tx_usuario_validacion, ''), v_usuario_id, CURRENT_DATE, v_id_referencia);
            END IF;

            -- Auditar cambio en dac_fh_validacion
            IF OLD.dac_fh_validacion IS DISTINCT FROM NEW.dac_fh_validacion THEN
                INSERT INTO dac_t_auditoria (dac_tx_tabla, dac_tx_columna, dac_tx_valor_anterior, dac_tx_valor_nuevo, dac_id_usuario, dac_fh_modificacion, dac_id_referencia)
                VALUES (v_tabla_nombre, 'dac_fh_validacion', COALESCE(OLD.dac_fh_validacion::varchar, ''), COALESCE(NEW.dac_fh_validacion::varchar, ''), v_usuario_id, CURRENT_DATE, v_id_referencia);
            END IF;

            -- Auditar cambio en dac_tx_comentario_validacion
            IF OLD.dac_tx_comentario_validacion IS DISTINCT FROM NEW.dac_tx_comentario_validacion THEN
                INSERT INTO dac_t_auditoria (dac_tx_tabla, dac_tx_columna, dac_tx_valor_anterior, dac_tx_valor_nuevo, dac_id_usuario, dac_fh_modificacion, dac_id_referencia)
                VALUES (v_tabla_nombre, 'dac_tx_comentario_validacion', COALESCE(OLD.dac_tx_comentario_validacion, ''), COALESCE(NEW.dac_tx_comentario_validacion, ''), v_usuario_id, CURRENT_DATE, v_id_referencia);
            END IF;

            -- Auditar cambio en dac_tx_observacion_rechazo
            IF OLD.dac_tx_observacion_rechazo IS DISTINCT FROM NEW.dac_tx_observacion_rechazo THEN
                INSERT INTO dac_t_auditoria (dac_tx_tabla, dac_tx_columna, dac_tx_valor_anterior, dac_tx_valor_nuevo, dac_id_usuario, dac_fh_modificacion, dac_id_referencia)
                VALUES (v_tabla_nombre, 'dac_tx_observacion_rechazo', COALESCE(OLD.dac_tx_observacion_rechazo, ''), COALESCE(NEW.dac_tx_observacion_rechazo, ''), v_usuario_id, CURRENT_DATE, v_id_referencia);
            END IF;
        END IF;

        -- Auditar cambios en dac_t_candidato
        IF TG_TABLE_NAME = 'dac_t_candidato' THEN
            -- Auditar cambio en dac_tx_nombre
            IF OLD.dac_tx_nombre IS DISTINCT FROM NEW.dac_tx_nombre THEN
                INSERT INTO dac_t_auditoria (dac_tx_tabla, dac_tx_columna, dac_tx_valor_anterior, dac_tx_valor_nuevo, dac_id_usuario, dac_fh_modificacion, dac_id_referencia)
                VALUES (v_tabla_nombre, 'dac_tx_nombre', COALESCE(OLD.dac_tx_nombre, ''), COALESCE(NEW.dac_tx_nombre, ''), v_usuario_id, CURRENT_DATE, v_id_referencia);
            END IF;

            -- Auditar cambio en dac_tx_apellido1
            IF OLD.dac_tx_apellido1 IS DISTINCT FROM NEW.dac_tx_apellido1 THEN
                INSERT INTO dac_t_auditoria (dac_tx_tabla, dac_tx_columna, dac_tx_valor_anterior, dac_tx_valor_nuevo, dac_id_usuario, dac_fh_modificacion, dac_id_referencia)
                VALUES (v_tabla_nombre, 'dac_tx_apellido1', COALESCE(OLD.dac_tx_apellido1, ''), COALESCE(NEW.dac_tx_apellido1, ''), v_usuario_id, CURRENT_DATE, v_id_referencia);
            END IF;

            -- Auditar cambio en dac_tx_apellido2
            IF OLD.dac_tx_apellido2 IS DISTINCT FROM NEW.dac_tx_apellido2 THEN
                INSERT INTO dac_t_auditoria (dac_tx_tabla, dac_tx_columna, dac_tx_valor_anterior, dac_tx_valor_nuevo, dac_id_usuario, dac_fh_modificacion, dac_id_referencia)
                VALUES (v_tabla_nombre, 'dac_tx_apellido2', COALESCE(OLD.dac_tx_apellido2, ''), COALESCE(NEW.dac_tx_apellido2, ''), v_usuario_id, CURRENT_DATE, v_id_referencia);
            END IF;

            -- Auditar cambio en dac_fk_tipo_candidato
            IF OLD.dac_fk_tipo_candidato IS DISTINCT FROM NEW.dac_fk_tipo_candidato THEN
                INSERT INTO dac_t_auditoria (dac_tx_tabla, dac_tx_columna, dac_tx_valor_anterior, dac_tx_valor_nuevo, dac_id_usuario, dac_fh_modificacion, dac_id_referencia)
                VALUES (v_tabla_nombre, 'dac_fk_tipo_candidato', COALESCE(OLD.dac_fk_tipo_candidato::varchar, ''), COALESCE(NEW.dac_fk_tipo_candidato::varchar, ''), v_usuario_id, CURRENT_DATE, v_id_referencia);
            END IF;

            -- Auditar cambio en dac_in_orden
            IF OLD.dac_in_orden IS DISTINCT FROM NEW.dac_in_orden THEN
                INSERT INTO dac_t_auditoria (dac_tx_tabla, dac_tx_columna, dac_tx_valor_anterior, dac_tx_valor_nuevo, dac_id_usuario, dac_fh_modificacion, dac_id_referencia)
                VALUES (v_tabla_nombre, 'dac_in_orden', COALESCE(OLD.dac_in_orden::varchar, ''), COALESCE(NEW.dac_in_orden::varchar, ''), v_usuario_id, CURRENT_DATE, v_id_referencia);
            END IF;

            -- Auditar cambio en dac_fk_candidatura
            IF OLD.dac_fk_candidatura IS DISTINCT FROM NEW.dac_fk_candidatura THEN
                INSERT INTO dac_t_auditoria (dac_tx_tabla, dac_tx_columna, dac_tx_valor_anterior, dac_tx_valor_nuevo, dac_id_usuario, dac_fh_modificacion, dac_id_referencia)
                VALUES (v_tabla_nombre, 'dac_fk_candidatura', COALESCE(OLD.dac_fk_candidatura::varchar, ''), COALESCE(NEW.dac_fk_candidatura::varchar, ''), v_usuario_id, CURRENT_DATE, v_id_referencia);
            END IF;

            -- Auditar cambio en dac_fk_estado_candidato
            IF OLD.dac_fk_estado_candidato IS DISTINCT FROM NEW.dac_fk_estado_candidato THEN
                INSERT INTO dac_t_auditoria (dac_tx_tabla, dac_tx_columna, dac_tx_valor_anterior, dac_tx_valor_nuevo, dac_id_usuario, dac_fh_modificacion, dac_id_referencia)
                VALUES (v_tabla_nombre, 'dac_fk_estado_candidato', COALESCE(OLD.dac_fk_estado_candidato::varchar, ''), COALESCE(NEW.dac_fk_estado_candidato::varchar, ''), v_usuario_id, CURRENT_DATE, v_id_referencia);
            END IF;

            -- Auditar cambio en dac_tx_usuario_validacion
            IF OLD.dac_tx_usuario_validacion IS DISTINCT FROM NEW.dac_tx_usuario_validacion THEN
                INSERT INTO dac_t_auditoria (dac_tx_tabla, dac_tx_columna, dac_tx_valor_anterior, dac_tx_valor_nuevo, dac_id_usuario, dac_fh_modificacion, dac_id_referencia)
                VALUES (v_tabla_nombre, 'dac_tx_usuario_validacion', COALESCE(OLD.dac_tx_usuario_validacion, ''), COALESCE(NEW.dac_tx_usuario_validacion, ''), v_usuario_id, CURRENT_DATE, v_id_referencia);
            END IF;

            -- Auditar cambio en dac_fh_validacion
            IF OLD.dac_fh_validacion IS DISTINCT FROM NEW.dac_fh_validacion THEN
                INSERT INTO dac_t_auditoria (dac_tx_tabla, dac_tx_columna, dac_tx_valor_anterior, dac_tx_valor_nuevo, dac_id_usuario, dac_fh_modificacion, dac_id_referencia)
                VALUES (v_tabla_nombre, 'dac_fh_validacion', COALESCE(OLD.dac_fh_validacion::varchar, ''), COALESCE(NEW.dac_fh_validacion::varchar, ''), v_usuario_id, CURRENT_DATE, v_id_referencia);
            END IF;

            -- Auditar cambio en dac_tx_comentario_validacion
            IF OLD.dac_tx_comentario_validacion IS DISTINCT FROM NEW.dac_tx_comentario_validacion THEN
                INSERT INTO dac_t_auditoria (dac_tx_tabla, dac_tx_columna, dac_tx_valor_anterior, dac_tx_valor_nuevo, dac_id_usuario, dac_fh_modificacion, dac_id_referencia)
                VALUES (v_tabla_nombre, 'dac_tx_comentario_validacion', COALESCE(OLD.dac_tx_comentario_validacion, ''), COALESCE(NEW.dac_tx_comentario_validacion, ''), v_usuario_id, CURRENT_DATE, v_id_referencia);
            END IF;

            -- Auditar cambio en dac_tx_observacion_rechazo
            IF OLD.dac_tx_observacion_rechazo IS DISTINCT FROM NEW.dac_tx_observacion_rechazo THEN
                INSERT INTO dac_t_auditoria (dac_tx_tabla, dac_tx_columna, dac_tx_valor_anterior, dac_tx_valor_nuevo, dac_id_usuario, dac_fh_modificacion, dac_id_referencia)
                VALUES (v_tabla_nombre, 'dac_tx_observacion_rechazo', COALESCE(OLD.dac_tx_observacion_rechazo, ''), COALESCE(NEW.dac_tx_observacion_rechazo, ''), v_usuario_id, CURRENT_DATE, v_id_referencia);
            END IF;

            -- Auditar cambio en dac_fk_declaracion
            IF OLD.dac_fk_declaracion IS DISTINCT FROM NEW.dac_fk_declaracion THEN
                INSERT INTO dac_t_auditoria (dac_tx_tabla, dac_tx_columna, dac_tx_valor_anterior, dac_tx_valor_nuevo, dac_id_usuario, dac_fh_modificacion, dac_id_referencia)
                VALUES (v_tabla_nombre, 'dac_fk_declaracion', COALESCE(OLD.dac_fk_declaracion::varchar, ''), COALESCE(NEW.dac_fk_declaracion::varchar, ''), v_usuario_id, CURRENT_DATE, v_id_referencia);
            END IF;
        END IF;
    END IF;

    -- Retornar el registro apropiado
    IF TG_OP = 'DELETE' THEN
        RETURN OLD;
    ELSE
        RETURN NEW;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Crear triggers para auditoría en dac_t_candidatura
DROP TRIGGER IF EXISTS tr_auditoria_candidatura ON dac_t_candidatura;
CREATE TRIGGER tr_auditoria_candidatura
    AFTER UPDATE ON dac_t_candidatura
    FOR EACH ROW
    EXECUTE FUNCTION fn_auditoria_cambios();

-- Crear triggers para auditoría en dac_t_candidato
DROP TRIGGER IF EXISTS tr_auditoria_candidato ON dac_t_candidato;
CREATE TRIGGER tr_auditoria_candidato
    AFTER UPDATE ON dac_t_candidato
    FOR EACH ROW
    EXECUTE FUNCTION fn_auditoria_cambios();
