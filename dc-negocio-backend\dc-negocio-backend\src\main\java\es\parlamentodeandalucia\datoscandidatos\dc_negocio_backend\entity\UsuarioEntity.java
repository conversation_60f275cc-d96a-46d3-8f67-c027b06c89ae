package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

@Entity
@Table(name = "dac_t_usuario")
public class UsuarioEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "dac_id_usuario")
    private Long id;

    @Column(name = "dac_tx_keycloak_id")
    private String keycloakId;

    @Column(name = "dac_fk_username", unique = true)
    private String username;

    @Column(name = "dac_tx_nombre")
    private String nombre;

    @Column(name = "dac_tx_apellido1")
    private String apellido1;

    @Column(name = "dac_tx_apellido2")
    private String apellido2;

    @Column(name = "dac_tx_nombre_completo")
    private String nombreCompleto;

    @Column(name = "dac_tx_email")
    private String email;

    @Column(name = "dac_tx_estado")
    private String estado;

    @Column(name = "dac_bo_activo")
    private Boolean activo;

    @Column(name = "dac_dt_fecha_creacion")
    private LocalDateTime fechaCreacion;

    @Column(name = "dac_dt_ultimo_acceso")
    private LocalDateTime ultimoAcceso;

    @Column(name = "dac_fk_identidad")
    private Long identidadId;

    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(name = "dac_t_usuario_rol",
        joinColumns = @JoinColumn(name = "dac_fk_usuario", referencedColumnName = "dac_id_usuario"),
        inverseJoinColumns = @JoinColumn(name = "dac_fk_rol", referencedColumnName = "dac_id_rol"))
    private Set<RolEntity> roles = new HashSet<>();

    // Constructor por defecto
    public UsuarioEntity() {
    }

    // Constructor
    public UsuarioEntity(String keycloakId, String username, String nombre, String apellido1,
            String apellido2, String email) {
        this.keycloakId = keycloakId;
        this.username = username;
        this.nombre = nombre;
        this.apellido1 = apellido1;
        this.apellido2 = apellido2;
        this.email = email;
        this.estado = "activo";
        this.activo = true;
        this.fechaCreacion = LocalDateTime.now();
        this.ultimoAcceso = LocalDateTime.now();
        this.nombreCompleto = buildNombreCompleto(nombre, apellido1, apellido2);
    }

    // Constructor con todos los campos
    public UsuarioEntity(String keycloakId, String username, String nombre, String apellido1,
            String apellido2, String email, RolEntity rol) {
        this.keycloakId = keycloakId;
        this.username = username;
        this.nombre = nombre;
        this.apellido1 = apellido1;
        this.apellido2 = apellido2;
        this.email = email;
        this.estado = "activo";
        this.activo = true;
        this.fechaCreacion = LocalDateTime.now();
        this.ultimoAcceso = LocalDateTime.now();
        this.nombreCompleto = buildNombreCompleto(nombre, apellido1, apellido2);
        this.roles = new HashSet<>();
        this.roles.add(rol);
    }

    // Método para construir el nombre completo
    private String buildNombreCompleto(String nombre, String apellido1, String apellido2) {
        StringBuilder nombreCompleto = new StringBuilder();

        if (nombre != null && !nombre.trim().isEmpty()) {
            nombreCompleto.append(nombre.trim());
        }

        if (apellido1 != null && !apellido1.trim().isEmpty()) {
            if (nombreCompleto.length() > 0) {
                nombreCompleto.append(" ");
            }
            nombreCompleto.append(apellido1.trim());
        }

        if (apellido2 != null && !apellido2.trim().isEmpty()) {
            if (nombreCompleto.length() > 0) {
                nombreCompleto.append(" ");
            }
            nombreCompleto.append(apellido2.trim());
        }

        return nombreCompleto.toString();
    }

    // Método para actualizar último acceso
    public void updateUltimoAcceso() {
        this.ultimoAcceso = LocalDateTime.now();
    }

    // Getters y Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getKeycloakId() {
        return keycloakId;
    }

    public void setKeycloakId(String keycloakId) {
        this.keycloakId = keycloakId;
    }



    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getNombre() {
        return nombre;
    }

    public void setNombre(String nombre) {
        this.nombre = nombre;
        this.nombreCompleto = buildNombreCompleto(this.nombre, this.apellido1, this.apellido2);
    }

    public String getApellido1() {
        return apellido1;
    }

    public void setApellido1(String apellido1) {
        this.apellido1 = apellido1;
        this.nombreCompleto = buildNombreCompleto(this.nombre, this.apellido1, this.apellido2);
    }

    public String getApellido2() {
        return apellido2;
    }

    public void setApellido2(String apellido2) {
        this.apellido2 = apellido2;
        this.nombreCompleto = buildNombreCompleto(this.nombre, this.apellido1, this.apellido2);
    }

    public String getNombreCompleto() {
        return nombreCompleto;
    }

    public void setNombreCompleto(String nombreCompleto) {
        this.nombreCompleto = nombreCompleto;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Set<RolEntity> getRoles() {
        return roles;
    }

    public void setRoles(Set<RolEntity> roles) {
        this.roles = roles;
    }

    public String getEstado() {
        return estado;
    }

    public void setEstado(String estado) {
        this.estado = estado;
    }

    public Boolean getActivo() {
        return activo;
    }

    public void setActivo(Boolean activo) {
        this.activo = activo;
    }

    public LocalDateTime getFechaCreacion() {
        return fechaCreacion;
    }

    public void setFechaCreacion(LocalDateTime fechaCreacion) {
        this.fechaCreacion = fechaCreacion;
    }

    public LocalDateTime getUltimoAcceso() {
        return ultimoAcceso;
    }

    public void setUltimoAcceso(LocalDateTime ultimoAcceso) {
        this.ultimoAcceso = ultimoAcceso;
    }

    @PrePersist
    protected void onCreate() {
        if (fechaCreacion == null) {
            fechaCreacion = LocalDateTime.now();
        }
        if (ultimoAcceso == null) {
            ultimoAcceso = LocalDateTime.now();
        }
        if (estado == null) {
            estado = "activo";
        }
        if (activo == null) {
            activo = true;
        }
        if (nombreCompleto == null) {
            nombreCompleto = buildNombreCompleto(nombre, apellido1, apellido2);
        }
    }

    @PreUpdate
    protected void onUpdate() {
        nombreCompleto = buildNombreCompleto(nombre, apellido1, apellido2);
    }
}