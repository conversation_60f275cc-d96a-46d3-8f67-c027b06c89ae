package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity;

import jakarta.persistence.*;
import java.time.LocalDate;
import java.util.HashSet;
import java.util.Set;

@Entity
@Table(name = "dac_t_usuario")
public class UsuarioEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "dac_id_usuario")
    private Long id;

    @Column(name = "dac_fk_username", unique = true, nullable = false)
    private String username;

    @Column(name = "dac_fh_alta")
    private LocalDate fechaAlta;

    @Column(name = "dac_fh_baja")
    private LocalDate fechaBaja;

    @Column(name = "dac_fh_last_login")
    private LocalDate fechaUltimoLogin;

    @Column(name = "dac_bl_activo")
    private Boolean activo;

    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(name = "dac_t_usuario_rol",
        joinColumns = @JoinColumn(name = "dac_fk_usuario", referencedColumnName = "dac_id_usuario"),
        inverseJoinColumns = @JoinColumn(name = "dac_fk_rol", referencedColumnName = "dac_id_rol"))
    private Set<RolEntity> roles = new HashSet<>();

    // Constructor por defecto
    public UsuarioEntity() {
    }

    // Constructor simplificado
    public UsuarioEntity(String username) {
        this.username = username;
        this.activo = true;
        this.fechaAlta = LocalDate.now();
    }

    // Constructor con rol
    public UsuarioEntity(String username, RolEntity rol) {
        this.username = username;
        this.activo = true;
        this.fechaAlta = LocalDate.now();
        this.roles = new HashSet<>();
        this.roles.add(rol);
    }

    // Método para actualizar último login
    public void updateUltimoLogin() {
        this.fechaUltimoLogin = LocalDate.now();
    }

    // Getters y Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public LocalDate getFechaAlta() {
        return fechaAlta;
    }

    public void setFechaAlta(LocalDate fechaAlta) {
        this.fechaAlta = fechaAlta;
    }

    public LocalDate getFechaBaja() {
        return fechaBaja;
    }

    public void setFechaBaja(LocalDate fechaBaja) {
        this.fechaBaja = fechaBaja;
    }

    public LocalDate getFechaUltimoLogin() {
        return fechaUltimoLogin;
    }

    public void setFechaUltimoLogin(LocalDate fechaUltimoLogin) {
        this.fechaUltimoLogin = fechaUltimoLogin;
    }

    public Boolean getActivo() {
        return activo;
    }

    public void setActivo(Boolean activo) {
        this.activo = activo;
    }

    public Set<RolEntity> getRoles() {
        return roles;
    }

    public void setRoles(Set<RolEntity> roles) {
        this.roles = roles;
    }

    @PrePersist
    protected void onCreate() {
        if (fechaAlta == null) {
            fechaAlta = LocalDate.now();
        }
        if (activo == null) {
            activo = true;
        }
    }
}