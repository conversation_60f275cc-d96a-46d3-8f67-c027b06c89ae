# Perfil LOCAL: ejecutando app en tu máquina y Postgres del docker-compose del proyecto (puerto 5433)

# Servidor sin SSL
server.port=8080
server.ssl.enabled=false

# Datasource
spring.datasource.url=************************************************
spring.datasource.username=postgres
spring.datasource.password=postgres
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA/Hibernate
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.properties.hibernate.use_sql_comments=false

# CORS para frontend local
spring.web.cors.allowed-origins=http://localhost:4200,http://localhost:3000
spring.web.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
spring.web.cors.allowed-headers=*
spring.web.cors.allow-credentials=true

# Logging
logging.level.es.parlamentodeandalucia.datoscandidatos=DEBUG
logging.level.org.springframework.web=WARN
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE
logging.level.org.springframework.security=WARN

# Keycloak (HTTP para local)
spring.security.oauth2.resourceserver.jwt.issuer-uri=https://***************:8453/realms/parlamento
spring.security.oauth2.resourceserver.jwt.jwk-set-uri=https://***************:8453/realms/parlamento/protocol/openid-connect/certs
keycloak.realm=parlamento
keycloak.auth-server-url=https://***************:8453
keycloak.resource=datosCandidatos
keycloak.public-client=true

# Swagger UI: usar YAML estático también en LOCAL
springdoc.swagger-ui.disable-swagger-default-url=true
springdoc.swagger-ui.url=/swagger.yaml

# Configuración de logs de importación CSV (local)
app.csv.logs.directory=${user.home}/Documents/ImportacionesCSV
app.csv.logs.enabled=true
app.csv.logs.max-files=50

