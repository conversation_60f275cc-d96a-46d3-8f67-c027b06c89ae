package es.parlamentodeandalucia.datoscandidatos.dto;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import org.hibernate.validator.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import jakarta.annotation.Generated;

/**
 * Candidato
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2025-08-18T12:08:24.473940700+02:00[Europe/Madrid]")
public class Candidato {

  private Integer id;

  private String nombre;

  private String apellidos;

  private String circunscripcion;

  private String formacionPolitica;

  /**
   * Gets or Sets estado
   */
  public enum EstadoEnum {
    PENDIENTE("pendiente"),
    
    FINALIZADA("finalizada"),
    
    PRESENTADA("presentada"),
    
    VALIDADA("validada");

    private String value;

    EstadoEnum(String value) {
      this.value = value;
    }

    @JsonValue
    public String getValue() {
      return value;
    }

    @Override
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static EstadoEnum fromValue(String value) {
      for (EstadoEnum b : EstadoEnum.values()) {
        if (b.value.equals(value)) {
          return b;
        }
      }
      throw new IllegalArgumentException("Unexpected value '" + value + "'");
    }
  }

  private EstadoEnum estado;

  public Candidato id(Integer id) {
    this.id = id;
    return this;
  }

  /**
   * Get id
   * @return id
  */
  
  @Schema(name = "id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("id")
  public Integer getId() {
    return id;
  }

  public void setId(Integer id) {
    this.id = id;
  }

  public Candidato nombre(String nombre) {
    this.nombre = nombre;
    return this;
  }

  /**
   * Get nombre
   * @return nombre
  */
  
  @Schema(name = "nombre", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("nombre")
  public String getNombre() {
    return nombre;
  }

  public void setNombre(String nombre) {
    this.nombre = nombre;
  }

  public Candidato apellidos(String apellidos) {
    this.apellidos = apellidos;
    return this;
  }

  /**
   * Get apellidos
   * @return apellidos
  */
  
  @Schema(name = "apellidos", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("apellidos")
  public String getApellidos() {
    return apellidos;
  }

  public void setApellidos(String apellidos) {
    this.apellidos = apellidos;
  }

  public Candidato circunscripcion(String circunscripcion) {
    this.circunscripcion = circunscripcion;
    return this;
  }

  /**
   * Get circunscripcion
   * @return circunscripcion
  */
  
  @Schema(name = "circunscripcion", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("circunscripcion")
  public String getCircunscripcion() {
    return circunscripcion;
  }

  public void setCircunscripcion(String circunscripcion) {
    this.circunscripcion = circunscripcion;
  }

  public Candidato formacionPolitica(String formacionPolitica) {
    this.formacionPolitica = formacionPolitica;
    return this;
  }

  /**
   * Get formacionPolitica
   * @return formacionPolitica
  */
  
  @Schema(name = "formacion_politica", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("formacion_politica")
  public String getFormacionPolitica() {
    return formacionPolitica;
  }

  public void setFormacionPolitica(String formacionPolitica) {
    this.formacionPolitica = formacionPolitica;
  }

  public Candidato estado(EstadoEnum estado) {
    this.estado = estado;
    return this;
  }

  /**
   * Get estado
   * @return estado
  */
  
  @Schema(name = "estado", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("estado")
  public EstadoEnum getEstado() {
    return estado;
  }

  public void setEstado(EstadoEnum estado) {
    this.estado = estado;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Candidato candidato = (Candidato) o;
    return Objects.equals(this.id, candidato.id) &&
        Objects.equals(this.nombre, candidato.nombre) &&
        Objects.equals(this.apellidos, candidato.apellidos) &&
        Objects.equals(this.circunscripcion, candidato.circunscripcion) &&
        Objects.equals(this.formacionPolitica, candidato.formacionPolitica) &&
        Objects.equals(this.estado, candidato.estado);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, nombre, apellidos, circunscripcion, formacionPolitica, estado);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Candidato {\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    nombre: ").append(toIndentedString(nombre)).append("\n");
    sb.append("    apellidos: ").append(toIndentedString(apellidos)).append("\n");
    sb.append("    circunscripcion: ").append(toIndentedString(circunscripcion)).append("\n");
    sb.append("    formacionPolitica: ").append(toIndentedString(formacionPolitica)).append("\n");
    sb.append("    estado: ").append(toIndentedString(estado)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

