package es.parlamentodeandalucia.datoscandidatos.dto;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import org.hibernate.validator.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import jakarta.annotation.Generated;

/**
 * Plantilla
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2025-08-18T12:09:52.242926500+02:00[Europe/Madrid]")
public class Plantilla {

  private Integer id;

  private String nombre;

  private String tipoDocumento;

  private Boolean activa;

  public Plantilla id(Integer id) {
    this.id = id;
    return this;
  }

  /**
   * Get id
   * @return id
  */
  
  @Schema(name = "id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("id")
  public Integer getId() {
    return id;
  }

  public void setId(Integer id) {
    this.id = id;
  }

  public Plantilla nombre(String nombre) {
    this.nombre = nombre;
    return this;
  }

  /**
   * Get nombre
   * @return nombre
  */
  
  @Schema(name = "nombre", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("nombre")
  public String getNombre() {
    return nombre;
  }

  public void setNombre(String nombre) {
    this.nombre = nombre;
  }

  public Plantilla tipoDocumento(String tipoDocumento) {
    this.tipoDocumento = tipoDocumento;
    return this;
  }

  /**
   * Get tipoDocumento
   * @return tipoDocumento
  */
  
  @Schema(name = "tipoDocumento", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("tipoDocumento")
  public String getTipoDocumento() {
    return tipoDocumento;
  }

  public void setTipoDocumento(String tipoDocumento) {
    this.tipoDocumento = tipoDocumento;
  }

  public Plantilla activa(Boolean activa) {
    this.activa = activa;
    return this;
  }

  /**
   * Get activa
   * @return activa
  */
  
  @Schema(name = "activa", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("activa")
  public Boolean getActiva() {
    return activa;
  }

  public void setActiva(Boolean activa) {
    this.activa = activa;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Plantilla plantilla = (Plantilla) o;
    return Objects.equals(this.id, plantilla.id) &&
        Objects.equals(this.nombre, plantilla.nombre) &&
        Objects.equals(this.tipoDocumento, plantilla.tipoDocumento) &&
        Objects.equals(this.activa, plantilla.activa);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, nombre, tipoDocumento, activa);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Plantilla {\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    nombre: ").append(toIndentedString(nombre)).append("\n");
    sb.append("    tipoDocumento: ").append(toIndentedString(tipoDocumento)).append("\n");
    sb.append("    activa: ").append(toIndentedString(activa)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

