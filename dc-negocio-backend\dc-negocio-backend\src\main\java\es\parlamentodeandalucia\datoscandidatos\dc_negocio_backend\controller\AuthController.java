package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.controller;

import es.parlamentodeandalucia.datoscandidatos.dto.Usuario;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.UsuarioEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service.UsuarioService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.Map;

@RestController
@RequestMapping("/api/auth")
@CrossOrigin(origins = {"http://localhost:4200", "https://localhost:4200"})
@Tag(name = "Autenticación", description = "Endpoints relacionados con la autenticación y gestión de usuarios")
public class AuthController {

    private static final Logger logger = LoggerFactory.getLogger(AuthController.class);

    @Autowired
    private UsuarioService usuarioService;

    @GetMapping("/me")
    @Operation(summary = "Obtener información del usuario autenticado",
               description = "Devuelve la información del usuario actual extraída del token JWT y sincronizada con la base de datos")
    @SecurityRequirement(name = "bearerAuth")
    public ResponseEntity<Usuario> getCurrentUser(@AuthenticationPrincipal Jwt jwt) {
        logger.info("=== ENDPOINT /auth/me LLAMADO ===");
        logger.info("JWT Subject: {}", jwt.getSubject());
        logger.info("JWT Claims: {}", jwt.getClaims());

        try {
            logger.info("=== INICIANDO SINCRONIZACIÓN DE USUARIO ===");
            logger.info("JWT Subject (Keycloak ID): {}", jwt.getSubject());
            logger.info("JWT Username: {}", jwt.getClaimAsString("username"));
            logger.info("JWT Email: {}", jwt.getClaimAsString("email"));

            // Sincronizar usuario de Keycloak con nuestra base de datos
            UsuarioEntity usuarioEntity = usuarioService.sincronizarUsuarioKeycloak(jwt);

            logger.info("✅ SINCRONIZACIÓN COMPLETADA");
            logger.info("Usuario guardado - ID: {}, Username: {}",
                usuarioEntity.getId(), usuarioEntity.getUsername());

            // Convertir entidad a DTO
            Usuario usuario = usuarioService.convertirADto(usuarioEntity);

            logger.info("Usuario DTO creado - Username: {}, Nombre completo: {}",
                usuario.getUsername(), usuario.getNombreCompleto());

            return ResponseEntity.ok(usuario);
        } catch (Exception e) {
            logger.error("❌ ERROR CRÍTICO en sincronización de usuario", e);
            logger.error("Detalles del error: {}", e.getMessage());
            logger.error("Tipo de excepción: {}", e.getClass().getSimpleName());

            // Imprimir stack trace completo para debugging
            e.printStackTrace();

            // En caso de error, devolver información básica del JWT
            // Esto asegura que el endpoint siempre funcione aunque haya problemas con la BD
            Usuario usuario = buildUsuarioFromJwt(jwt);

            logger.warn("⚠️ USANDO FALLBACK - Devolviendo usuario desde JWT - Username: {}", usuario.getUsername());

            return ResponseEntity.ok(usuario);
        }
    }

    /**
     * Método de respaldo para construir usuario desde JWT en caso de error con BD
     */
    private Usuario buildUsuarioFromJwt(Jwt jwt) {
        Usuario usuario = new Usuario();

        // Extraer campos individuales del JWT
        String nombre = jwt.getClaim("nombre");
        String apellido1 = jwt.getClaim("apellido1");
        String apellido2 = jwt.getClaim("apellido2");
        String username = jwt.getClaim("username");

        // Mapear información del JWT al DTO Usuario
        usuario.setId(extractUserIdFromJwt(jwt));
        usuario.setUsername(username);
        usuario.setNombre(nombre);
        usuario.setApellido1(apellido1);
        usuario.setApellido2(apellido2);
        usuario.setNombreCompleto(buildNombreCompleto(nombre, apellido1, apellido2));
        usuario.setEmail(jwt.getClaim("email"));
        usuario.setRol(extractPrimaryRole(jwt));
        usuario.setEstado(Usuario.EstadoEnum.ACTIVO);

        return usuario;
    }

    private Integer extractUserIdFromJwt(Jwt jwt) {
        // Intentar extraer ID del usuario desde el JWT
        String sub = jwt.getSubject();
        try {
            // Si el subject es un UUID, generar un ID numérico basado en el hash
            return Math.abs(sub.hashCode());
        } catch (Exception e) {
            return 1; // ID por defecto
        }
    }

    private String extractPrimaryRole(Jwt jwt) {
        // Extraer el rol principal del usuario desde Keycloak
        Map<String, Object> resourceAccess = jwt.getClaim("resource_access");
        if (resourceAccess != null) {
            @SuppressWarnings("unchecked")
            Map<String, Object> clientAccess = (Map<String, Object>) resourceAccess.get("datosCandidatos");
            if (clientAccess != null) {
                @SuppressWarnings("unchecked")
                Collection<String> roles = (Collection<String>) clientAccess.get("roles");
                if (roles != null && !roles.isEmpty()) {
                    return roles.iterator().next().toUpperCase();
                }
            }
        }
        return "USER"; // Rol por defecto
    }

    private String buildNombreCompleto(String nombre, String apellido1, String apellido2) {
        // Construir el nombre completo combinando nombre + apellido1 + apellido2
        StringBuilder nombreCompleto = new StringBuilder();

        if (nombre != null && !nombre.trim().isEmpty()) {
            nombreCompleto.append(nombre.trim());
        }

        if (apellido1 != null && !apellido1.trim().isEmpty()) {
            if (nombreCompleto.length() > 0) {
                nombreCompleto.append(" ");
            }
            nombreCompleto.append(apellido1.trim());
        }

        if (apellido2 != null && !apellido2.trim().isEmpty()) {
            if (nombreCompleto.length() > 0) {
                nombreCompleto.append(" ");
            }
            nombreCompleto.append(apellido2.trim());
        }

        return nombreCompleto.toString();
    }
}
