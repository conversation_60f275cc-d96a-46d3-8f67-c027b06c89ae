<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="56608d05-5efe-4e31-b1ed-65ba48391acc" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/src/main/java/es/parlamentodeandalucia/datoscandidatos/dc_negocio_backend/config/AuditContextFilter.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/es/parlamentodeandalucia/datoscandidatos/dc_negocio_backend/config/AuditContextFilter.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/es/parlamentodeandalucia/datoscandidatos/dc_negocio_backend/controller/AuthController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/es/parlamentodeandalucia/datoscandidatos/dc_negocio_backend/controller/AuthController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/es/parlamentodeandalucia/datoscandidatos/dc_negocio_backend/controller/UsuarioController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/es/parlamentodeandalucia/datoscandidatos/dc_negocio_backend/controller/UsuarioController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/es/parlamentodeandalucia/datoscandidatos/dc_negocio_backend/entity/UsuarioEntity.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/es/parlamentodeandalucia/datoscandidatos/dc_negocio_backend/entity/UsuarioEntity.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/es/parlamentodeandalucia/datoscandidatos/dc_negocio_backend/repository/UsuarioRepository.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/es/parlamentodeandalucia/datoscandidatos/dc_negocio_backend/repository/UsuarioRepository.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/es/parlamentodeandalucia/datoscandidatos/dc_negocio_backend/service/UsuarioService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/es/parlamentodeandalucia/datoscandidatos/dc_negocio_backend/service/UsuarioService.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="mavenHomeTypeForPersistence" value="WRAPPER" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="2zdOkhEL9607RieFwrvJifw8u98" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Application.DcNegocioBackendApplication.executor&quot;: &quot;Run&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;develop&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/Documents/Datos candidatos/dc-negocio-backend/dc-negocio-backend&quot;
  }
}</component>
  <component name="RunManager">
    <configuration name="DcNegocioBackendApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.DcNegocioBackendApplication" />
      <module name="dc-negocio-backend" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Application.DcNegocioBackendApplication" />
      </list>
    </recent_temporary>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="56608d05-5efe-4e31-b1ed-65ba48391acc" name="Changes" comment="" />
      <created>1752057627938</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752057627938</updated>
    </task>
    <servers />
  </component>
</project>