package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.controller;

import java.util.List;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.FuncionalidadEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.repository.PermisoRepository;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service.FuncionalidadService;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * Controlador REST para gestionar las funcionalidades del sistema.
 * Incluye operaciones CRUD y activación/desactivación de funcionalidades.
 */
@RestController
@RequestMapping("/api/funcionalidades")
@Tag(name = "Funcionalidad", description = "Operaciones sobre funcionalidades")
@CrossOrigin(origins = { "http://localhost:4200", "https://localhost:4200" })
public class FuncionalidadController {

    private static final Logger logger = LoggerFactory.getLogger(FuncionalidadController.class);

    @Autowired
    private FuncionalidadService funcionalidadService;
    
    @Autowired
    private PermisoRepository permisoRepository;

    public FuncionalidadController(FuncionalidadService funcionalidadService) {
        this.funcionalidadService = funcionalidadService;
    }

    /**
     * Obtener todas las funcionalidades.
     */
    @GetMapping
    @PreAuthorize("hasRole('ADMIN')")
    public List<FuncionalidadEntity> getAllFuncionalidades() {
        logger.info("Listando todas las funcionalidades");
        return funcionalidadService.findAll();
    }

    /**
     * Obtener una funcionalidad por ID.
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<FuncionalidadEntity> getFuncionalidadById(@PathVariable Long id) {
        logger.info("Buscando funcionalidad con ID {}", id);
        Optional<FuncionalidadEntity> funcionalidad = funcionalidadService.findById(id);
        return funcionalidad.map(ResponseEntity::ok).orElse(ResponseEntity.notFound().build());
    }

    /**
     * Crear una nueva funcionalidad.
     */
    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<FuncionalidadEntity> createFuncionalidad(@RequestBody FuncionalidadEntity funcionalidad) {
        logger.info("Creando nueva funcionalidad: {}", funcionalidad.getNombre());
        FuncionalidadEntity saved = funcionalidadService.save(funcionalidad);
        return new ResponseEntity<>(saved, HttpStatus.CREATED);
    }

    /**
     * Actualizar una funcionalidad existente.
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<FuncionalidadEntity> updateFuncionalidad(@PathVariable Long id, @RequestBody FuncionalidadEntity funcionalidadDetails) {
        logger.info("Actualizando funcionalidad con ID {}", id);
        try {
            FuncionalidadEntity updated = funcionalidadService.updateFuncionalidad(id, funcionalidadDetails);
            return ResponseEntity.ok(updated);
        } catch (RuntimeException e) {
            logger.warn("No se encontró la funcionalidad con ID {} para actualizar", id);
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Eliminar una funcionalidad por ID.
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deleteFuncionalidad(@PathVariable Long id) {
        logger.info("Eliminando funcionalidad con ID {}", id);

        Optional<FuncionalidadEntity> funcionalidadOpt = funcionalidadService.findById(id);
        if (!funcionalidadOpt.isPresent()) {
            logger.warn("No se encontró la funcionalidad con ID {} para eliminar", id);
            return ResponseEntity.notFound().build();
        }
        
        boolean tienePermisosAsociados = permisoRepository.existsByFuncionalidadId(id);
        if (tienePermisosAsociados) {
            logger.warn("No se puede eliminar la funcionalidad con ID {} porque tiene permisos asociados", id);
            return ResponseEntity.status(HttpStatus.CONFLICT).build(); // HTTP 409
        }
        
        funcionalidadService.deleteById(id);
        return ResponseEntity.noContent().build();
    }


    /**
     * Activar o desactivar una funcionalidad.
     */
    @PutMapping("/{id}/activo")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<FuncionalidadEntity> toggleActivo(@PathVariable Long id, @RequestParam boolean activo) {
        logger.info("{} funcionalidad con ID {}", activo ? "Activando" : "Desactivando", id);
        try {
            FuncionalidadEntity updated = funcionalidadService.toggleActivo(id, activo);
            return ResponseEntity.ok(updated);
        } catch (RuntimeException e) {
            logger.warn("No se encontró la funcionalidad con ID {} para cambio de estado", id);
            return ResponseEntity.notFound().build();
        }
    }

    // [FUTURO] Agregar paginación: getAllFuncionalidades(Pageable pageable)
}

