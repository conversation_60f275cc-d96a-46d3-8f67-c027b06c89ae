package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service;

import es.parlamentodeandalucia.datoscandidatos.dto.Usuario;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.RolEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.UsuarioEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.repository.RolRepository;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.repository.UsuarioRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;

@Service
@Transactional
public class UsuarioService {
    private static final Logger logger = LoggerFactory.getLogger(UsuarioService.class);

    @Autowired
    private UsuarioRepository usuarioRepository;

    @Autowired
    private RolRepository rolRepository;

    // Buscar todos los usuarios
    public List<UsuarioEntity> buscarTodos() {
        return usuarioRepository.findAll();
    }

    // Buscar por nombre
    public List<UsuarioEntity> buscarPorNombre(String nombre) {
        return usuarioRepository.findByNombreContainingIgnoreCase(nombre);
    }

    // Buscar por email
    public List<UsuarioEntity> buscarPorEmail(String email) {
        return usuarioRepository.findByEmailContainingIgnoreCase(email);
    }

    // Buscar todos los usuarios que tengan un rol específico
    public List<UsuarioEntity> buscarUsuariosPorRol(String nombreRol) {
        return usuarioRepository.findByRolesValorIgnoreCase(nombreRol);
    }

    // Buscar usuarios por rol y estado
    public List<UsuarioEntity> buscarUsuariosPorRolYEstado(String nombreRol, String estado) {
        return usuarioRepository.findByRolesValorIgnoreCaseAndEstadoIgnoreCase(nombreRol, estado);
    }

    // Actualizar estado y activo de un usuario
    public UsuarioEntity actualizarEstado(UsuarioEntity usuario) {
        Optional<UsuarioEntity> existente = usuarioRepository.findByUsername(usuario.getUsername());
        if (existente.isPresent()) {
            UsuarioEntity u = existente.get();
            u.setEstado(usuario.getEstado());
            u.setActivo("activo".equalsIgnoreCase(usuario.getEstado()));
            return usuarioRepository.save(u);
        } else {
            throw new RuntimeException("Usuario no encontrado con username: " + usuario.getUsername());
        }
    }

    // Modificar todos los datos de un usuario
    public UsuarioEntity actualizarUsuario(UsuarioEntity usuario) {
        return usuarioRepository.save(usuario);
    }

    // Eliminar un usuario por su username
    public boolean eliminarUsuario(String username) {
        Optional<UsuarioEntity> usuario = usuarioRepository.findByUsername(username);
        if (usuario.isPresent()) {
            usuarioRepository.delete(usuario.get());
            return true;
        }
        return false;
    }

    // Crear un nuevo usuario
    public UsuarioEntity crearUsuario(UsuarioEntity usuario) {
        return usuarioRepository.save(usuario);
    }

    // Buscar usuario por username
    public Optional<UsuarioEntity> buscarPorUsername(String username) {
        return usuarioRepository.findByUsername(username);
    }

    // Método requerido por AuthController: sincronizar usuario desde Keycloak
    public UsuarioEntity sincronizarUsuarioKeycloak(Jwt jwt) {
        logger.info("=== SINCRONIZANDO USUARIO DESDE KEYCLOAK ===");
        logger.info("JWT Claims completos: {}", jwt.getClaims());

        String keycloakId = jwt.getSubject();
        String username = jwt.getClaimAsString("username");
        String email = jwt.getClaimAsString("email");
        String nombre = jwt.getClaimAsString("nombre");
        String apellido1 = jwt.getClaimAsString("apellido1");
        String apellido2 = jwt.getClaimAsString("apellido2");

        logger.info("Datos extraídos - keycloakId: {}, username: {}, email: {}, nombre: {}, apellido1: {}, apellido2: {}",
                   keycloakId, username, email, nombre, apellido1, apellido2);

        // Buscar usuario existente por username (clave principal)
        logger.info("Buscando usuario por username: {}", username);
        Optional<UsuarioEntity> opt = Optional.empty();

        if (username != null && !username.trim().isEmpty()) {
            opt = usuarioRepository.findByUsername(username);
        } else {
            logger.warn("Username es null o vacío, no se puede buscar usuario");
        }

        UsuarioEntity usuario;
        if (opt.isPresent()) {
            logger.info("✅ Usuario encontrado en BD - ID: {}, Username: {}", opt.get().getId(), opt.get().getUsername());
            usuario = opt.get();
            usuario.setUltimoAcceso(LocalDateTime.now());

            // Actualizar datos si han cambiado
            if (username != null && !username.equals(usuario.getUsername())) {
                logger.info("Actualizando username: {} -> {}", usuario.getUsername(), username);
                usuario.setUsername(username);
            }
            if (email != null && !email.equals(usuario.getEmail())) {
                logger.info("Actualizando email: {} -> {}", usuario.getEmail(), email);
                usuario.setEmail(email);
            }
            if (nombre != null && !nombre.equals(usuario.getNombre())) {
                logger.info("Actualizando nombre: {} -> {}", usuario.getNombre(), nombre);
                usuario.setNombre(nombre);
            }
            if (apellido1 != null && !apellido1.equals(usuario.getApellido1())) {
                logger.info("Actualizando apellido1: {} -> {}", usuario.getApellido1(), apellido1);
                usuario.setApellido1(apellido1);
            }
            if (apellido2 != null && !apellido2.equals(usuario.getApellido2())) {
                logger.info("Actualizando apellido2: {} -> {}", usuario.getApellido2(), apellido2);
                usuario.setApellido2(apellido2);
            }
        } else {
            logger.info("🆕 Creando nuevo usuario en BD");
            // Crear nuevo usuario con datos del JWT
            usuario = new UsuarioEntity();

            // IMPORTANTE: username es NOT NULL, asegurar que siempre tenga valor
            String usernameValue = username;
            if (usernameValue == null || usernameValue.trim().isEmpty()) {
                // Si no hay username, no podemos crear el usuario
                throw new IllegalArgumentException("Username es requerido para crear usuario");
            }
            usuario.setUsername(usernameValue);

            usuario.setEmail(email != null ? email : "");
            usuario.setNombre(nombre != null ? nombre : (usernameValue));
            usuario.setApellido1(apellido1 != null ? apellido1 : "");
            usuario.setApellido2(apellido2 != null ? apellido2 : "");
            usuario.setEstado("activo");
            usuario.setFechaCreacion(LocalDateTime.now());
            usuario.setUltimoAcceso(LocalDateTime.now());

            // Asignar rol por defecto
            RolEntity rolDefault = rolRepository.findByValorIgnoreCase("USER")
                    .orElse(rolRepository.findByValorIgnoreCase("CANDIDATO")
                            .orElse(null));
            if (rolDefault != null) {
                usuario.setRoles(Set.of(rolDefault));
            }
        }

        // Validaciones antes de guardar
        if (usuario.getUsername() == null || usuario.getUsername().trim().isEmpty()) {
            throw new IllegalArgumentException("Username no puede ser null o vacío (campo NOT NULL en BD)");
        }

        try {
            logger.info("💾 Guardando usuario en BD...");
            logger.info("Usuario antes de guardar - ID: {}, Keycloak ID: {}, Username: {}, Email: {}",
                usuario.getId(), usuario.getKeycloakId(), usuario.getUsername(), usuario.getEmail());
            logger.info("Validación OK - Username: '{}' (length: {})", usuario.getUsername(), usuario.getUsername().length());

            UsuarioEntity usuarioGuardado = usuarioRepository.save(usuario);

            logger.info("✅ Usuario guardado exitosamente - ID: {}, Keycloak ID: {}",
                usuarioGuardado.getId(), usuarioGuardado.getKeycloakId());

            return usuarioGuardado;
        } catch (Exception e) {
            logger.error("❌ ERROR al guardar usuario en BD", e);
            logger.error("Detalles del usuario que falló: Keycloak ID: {}, Username: {}, Email: {}",
                usuario.getKeycloakId(), usuario.getUsername(), usuario.getEmail());
            logger.error("Tipo de excepción: {}", e.getClass().getSimpleName());
            logger.error("Mensaje de error: {}", e.getMessage());
            throw e;
        }
    }

    // Método requerido por AuthController: convertir UsuarioEntity a DTO Usuario
    public Usuario convertirADto(UsuarioEntity entity) {
        Usuario dto = new Usuario();
        dto.setId(Math.abs(entity.getUsername().hashCode())); // Generamos ID artificial basado en username
        dto.setUsername(entity.getUsername());
        dto.setNombre(entity.getNombre());
        dto.setApellido1(entity.getApellido1());
        dto.setApellido2(entity.getApellido2());
        dto.setNombreCompleto(entity.getNombreCompleto());
        dto.setEmail(entity.getEmail());
        dto.setRol(
                entity.getRoles().stream()
                        .map(RolEntity::getValor)
                        .findFirst() // o `.collect(Collectors.toList())` si quieres todos
                        .orElse("SIN_ROL"));

        dto.setEstado(entity.getEstado() != null && entity.getEstado().equalsIgnoreCase("activo")
                ? Usuario.EstadoEnum.ACTIVO
                : Usuario.EstadoEnum.INACTIVO);
        return dto;
    }



    // Asignar roles a un usuario por su username
    public UsuarioEntity asignarRoles(String username, List<Long> idsRoles) {
        UsuarioEntity usuario = usuarioRepository.findByUsername(username)
                .orElseThrow(() -> new RuntimeException("Usuario no encontrado"));

        Set<RolEntity> nuevosRoles = new HashSet<>(rolRepository.findAllById(idsRoles));
        usuario.setRoles(nuevosRoles); // reemplaza por completo
        return usuarioRepository.save(usuario);
    }

}
