{"version": 3, "sources": ["../../../../../../node_modules/primeng/fesm2022/primeng-scroller.mjs"], "sourcesContent": ["import * as i1 from '@angular/common';\nimport { isPlatformBrowser, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, inject, ContentChildren, ContentChild, HostBinding, ViewChild, Output, Input, ViewEncapsulation, ChangeDetectionStrategy, Component, NgModule } from '@angular/core';\nimport { isVisible, getWidth, getHeight, findSingle, isTouchDevice } from '@primeuix/utils';\nimport { SharedModule, PrimeTemplate } from 'primeng/api';\nimport { BaseComponent } from 'primeng/basecomponent';\nimport { SpinnerIcon } from 'primeng/icons';\nimport { BaseStyle } from 'primeng/base';\nconst _c0 = [\"content\"];\nconst _c1 = [\"item\"];\nconst _c2 = [\"loader\"];\nconst _c3 = [\"loadericon\"];\nconst _c4 = [\"element\"];\nconst _c5 = [\"*\"];\nconst _c6 = (a0, a1, a2) => ({\n  \"p-virtualscroller\": true,\n  \"p-virtualscroller-inline\": a0,\n  \"p-virtualscroller-both p-both-scroll\": a1,\n  \"p-virtualscroller-horizontal p-horizontal-scroll\": a2\n});\nconst _c7 = (a0, a1) => ({\n  $implicit: a0,\n  options: a1\n});\nconst _c8 = a0 => ({\n  \"p-virtualscroller-content\": true,\n  \"p-virtualscroller-loading \": a0\n});\nconst _c9 = a0 => ({\n  \"p-virtualscroller-loader-mask\": a0\n});\nconst _c10 = a0 => ({\n  numCols: a0\n});\nconst _c11 = a0 => ({\n  options: a0\n});\nconst _c12 = () => ({\n  styleClass: \"p-virtualscroller-loading-icon\"\n});\nconst _c13 = (a0, a1) => ({\n  rows: a0,\n  columns: a1\n});\nfunction Scroller_ng_container_0_ng_container_3_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Scroller_ng_container_0_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Scroller_ng_container_0_ng_container_3_ng_container_1_Template, 1, 0, \"ng-container\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.contentTemplate || ctx_r1._contentTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c7, ctx_r1.loadedItems, ctx_r1.getContentOptions()));\n  }\n}\nfunction Scroller_ng_container_0_ng_template_4_ng_container_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Scroller_ng_container_0_ng_template_4_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Scroller_ng_container_0_ng_template_4_ng_container_2_ng_container_1_Template, 1, 0, \"ng-container\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.$implicit;\n    const index_r4 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.itemTemplate || ctx_r1._itemTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c7, item_r3, ctx_r1.getOptions(index_r4)));\n  }\n}\nfunction Scroller_ng_container_0_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11, 3);\n    i0.ɵɵtemplate(2, Scroller_ng_container_0_ng_template_4_ng_container_2_Template, 2, 5, \"ng-container\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleMap(ctx_r1.contentStyle);\n    i0.ɵɵclassMap(ctx_r1.contentStyleClass);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(8, _c8, ctx_r1.d_loading));\n    i0.ɵɵattribute(\"data-pc-section\", \"content\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.loadedItems)(\"ngForTrackBy\", ctx_r1._trackBy);\n  }\n}\nfunction Scroller_ng_container_0_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 13);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngStyle\", ctx_r1.spacerStyle);\n    i0.ɵɵattribute(\"data-pc-section\", \"spacer\");\n  }\n}\nfunction Scroller_ng_container_0_div_7_ng_container_1_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Scroller_ng_container_0_div_7_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Scroller_ng_container_0_div_7_ng_container_1_ng_container_1_ng_container_1_Template, 1, 0, \"ng-container\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const index_r5 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.loaderTemplate || ctx_r1._loaderTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(4, _c11, ctx_r1.getLoaderOptions(index_r5, ctx_r1.both && i0.ɵɵpureFunction1(2, _c10, ctx_r1.numItemsInViewport.cols))));\n  }\n}\nfunction Scroller_ng_container_0_div_7_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Scroller_ng_container_0_div_7_ng_container_1_ng_container_1_Template, 2, 6, \"ng-container\", 15);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.loaderArr);\n  }\n}\nfunction Scroller_ng_container_0_div_7_ng_template_2_ng_container_0_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Scroller_ng_container_0_div_7_ng_template_2_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Scroller_ng_container_0_div_7_ng_template_2_ng_container_0_ng_container_1_Template, 1, 0, \"ng-container\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.loaderIconTemplate || ctx_r1._loaderIconTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(3, _c11, i0.ɵɵpureFunction0(2, _c12)));\n  }\n}\nfunction Scroller_ng_container_0_div_7_ng_template_2_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"SpinnerIcon\", 16);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-virtualscroller-loading-icon pi-spin\");\n    i0.ɵɵattribute(\"data-pc-section\", \"loadingIcon\");\n  }\n}\nfunction Scroller_ng_container_0_div_7_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Scroller_ng_container_0_div_7_ng_template_2_ng_container_0_Template, 2, 5, \"ng-container\", 6)(1, Scroller_ng_container_0_div_7_ng_template_2_ng_template_1_Template, 1, 2, \"ng-template\", null, 5, i0.ɵɵtemplateRefExtractor);\n  }\n  if (rf & 2) {\n    const buildInLoaderIcon_r6 = i0.ɵɵreference(2);\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.loaderIconTemplate || ctx_r1._loaderIconTemplate)(\"ngIfElse\", buildInLoaderIcon_r6);\n  }\n}\nfunction Scroller_ng_container_0_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵtemplate(1, Scroller_ng_container_0_div_7_ng_container_1_Template, 2, 1, \"ng-container\", 6)(2, Scroller_ng_container_0_div_7_ng_template_2_Template, 3, 2, \"ng-template\", null, 4, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const buildInLoader_r7 = i0.ɵɵreference(3);\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c9, !ctx_r1.loaderTemplate));\n    i0.ɵɵattribute(\"data-pc-section\", \"loader\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.loaderTemplate || ctx_r1._loaderTemplate)(\"ngIfElse\", buildInLoader_r7);\n  }\n}\nfunction Scroller_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 7, 1);\n    i0.ɵɵlistener(\"scroll\", function Scroller_ng_container_0_Template_div_scroll_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onContainerScroll($event));\n    });\n    i0.ɵɵtemplate(3, Scroller_ng_container_0_ng_container_3_Template, 2, 5, \"ng-container\", 6)(4, Scroller_ng_container_0_ng_template_4_Template, 3, 10, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(6, Scroller_ng_container_0_div_6_Template, 1, 2, \"div\", 8)(7, Scroller_ng_container_0_div_7_Template, 4, 6, \"div\", 9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const buildInContent_r8 = i0.ɵɵreference(5);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r1._styleClass);\n    i0.ɵɵproperty(\"ngStyle\", ctx_r1._style)(\"ngClass\", i0.ɵɵpureFunction3(12, _c6, ctx_r1.inline, ctx_r1.both, ctx_r1.horizontal));\n    i0.ɵɵattribute(\"id\", ctx_r1._id)(\"tabindex\", ctx_r1.tabindex)(\"data-pc-name\", \"scroller\")(\"data-pc-section\", \"root\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.contentTemplate || ctx_r1._contentTemplate)(\"ngIfElse\", buildInContent_r8);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1._showSpacer);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.loaderDisabled && ctx_r1._showLoader && ctx_r1.d_loading);\n  }\n}\nfunction Scroller_ng_template_1_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Scroller_ng_template_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Scroller_ng_template_1_ng_container_1_ng_container_1_Template, 1, 0, \"ng-container\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.contentTemplate || ctx_r1._contentTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(5, _c7, ctx_r1.items, i0.ɵɵpureFunction2(2, _c13, ctx_r1._items, ctx_r1.loadedColumns)));\n  }\n}\nfunction Scroller_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n    i0.ɵɵtemplate(1, Scroller_ng_template_1_ng_container_1_Template, 2, 8, \"ng-container\", 17);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.contentTemplate || ctx_r1._contentTemplate);\n  }\n}\nconst theme = ({\n  dt\n}) => `\n.p-virtualscroller {\n    position: relative;\n    overflow: auto;\n    contain: strict;\n    transform: translateZ(0);\n    will-change: scroll-position;\n    outline: 0 none;\n}\n\n.p-virtualscroller-content {\n    position: absolute;\n    top: 0;\n    left: 0;\n    min-height: 100%;\n    min-width: 100%;\n    will-change: transform;\n}\n\n.p-virtualscroller-spacer {\n    position: absolute;\n    top: 0;\n    left: 0;\n    height: 1px;\n    width: 1px;\n    transform-origin: 0 0;\n    pointer-events: none;\n}\n\n.p-virtualscroller-loader {\n    position: sticky;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    background: ${dt('virtualscroller.loader.mask.background')};\n    color: ${dt('virtualscroller.loader.mask.color')};\n}\n\n.p-virtualscroller-loader-mask {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n}\n\n.p-virtualscroller-loading-icon {\n    font-size: ${dt('virtualscroller.loader.icon.size')};\n    width: ${dt('virtualscroller.loader.icon.size')};\n    height: ${dt('virtualscroller.loader.icon.size')};\n}\n\n.p-virtualscroller-horizontal > .p-virtualscroller-content {\n    display: flex;\n}\n\n.p-virtualscroller-inline .p-virtualscroller-content {\n    position: static;\n}\n`;\nclass ScrollerStyle extends BaseStyle {\n  name = 'virtualscroller';\n  theme = theme;\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵScrollerStyle_BaseFactory;\n    return function ScrollerStyle_Factory(__ngFactoryType__) {\n      return (ɵScrollerStyle_BaseFactory || (ɵScrollerStyle_BaseFactory = i0.ɵɵgetInheritedFactory(ScrollerStyle)))(__ngFactoryType__ || ScrollerStyle);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: ScrollerStyle,\n    factory: ScrollerStyle.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ScrollerStyle, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n *\n * VirtualScroller is a performant approach to handle huge data efficiently.\n *\n * [Live Demo](https://www.primeng.org/scroller/)\n *\n * @module scrollerstyle\n *\n */\nvar ScrollerClasses;\n(function (ScrollerClasses) {\n  /**\n   * Class name of the root element\n   */\n  ScrollerClasses[\"root\"] = \"p-virtualscroller\";\n  /**\n   * Class name of the content element\n   */\n  ScrollerClasses[\"content\"] = \"p-virtualscroller-content\";\n  /**\n   * Class name of the spacer element\n   */\n  ScrollerClasses[\"spacer\"] = \"p-virtualscroller-spacer\";\n  /**\n   * Class name of the loader element\n   */\n  ScrollerClasses[\"loader\"] = \"p-virtualscroller-loader\";\n  /**\n   * Class name of the loading icon element\n   */\n  ScrollerClasses[\"loadingIcon\"] = \"p-virtualscroller-loading-icon\";\n})(ScrollerClasses || (ScrollerClasses = {}));\n\n/**\n * Scroller is a performance-approach to handle huge data efficiently.\n * @group Components\n */\nclass Scroller extends BaseComponent {\n  zone;\n  /**\n   * Unique identifier of the element.\n   * @group Props\n   */\n  get id() {\n    return this._id;\n  }\n  set id(val) {\n    this._id = val;\n  }\n  /**\n   * Inline style of the component.\n   * @group Props\n   */\n  get style() {\n    return this._style;\n  }\n  set style(val) {\n    this._style = val;\n  }\n  /**\n   * Style class of the element.\n   * @group Props\n   */\n  get styleClass() {\n    return this._styleClass;\n  }\n  set styleClass(val) {\n    this._styleClass = val;\n  }\n  /**\n   * Index of the element in tabbing order.\n   * @group Props\n   */\n  get tabindex() {\n    return this._tabindex;\n  }\n  set tabindex(val) {\n    this._tabindex = val;\n  }\n  /**\n   * An array of objects to display.\n   * @group Props\n   */\n  get items() {\n    return this._items;\n  }\n  set items(val) {\n    this._items = val;\n  }\n  /**\n   * The height/width of item according to orientation.\n   * @group Props\n   */\n  get itemSize() {\n    return this._itemSize;\n  }\n  set itemSize(val) {\n    this._itemSize = val;\n  }\n  /**\n   * Height of the scroll viewport.\n   * @group Props\n   */\n  get scrollHeight() {\n    return this._scrollHeight;\n  }\n  set scrollHeight(val) {\n    this._scrollHeight = val;\n  }\n  /**\n   * Width of the scroll viewport.\n   * @group Props\n   */\n  get scrollWidth() {\n    return this._scrollWidth;\n  }\n  set scrollWidth(val) {\n    this._scrollWidth = val;\n  }\n  /**\n   * The orientation of scrollbar.\n   * @group Props\n   */\n  get orientation() {\n    return this._orientation;\n  }\n  set orientation(val) {\n    this._orientation = val;\n  }\n  /**\n   * Used to specify how many items to load in each load method in lazy mode.\n   * @group Props\n   */\n  get step() {\n    return this._step;\n  }\n  set step(val) {\n    this._step = val;\n  }\n  /**\n   * Delay in scroll before new data is loaded.\n   * @group Props\n   */\n  get delay() {\n    return this._delay;\n  }\n  set delay(val) {\n    this._delay = val;\n  }\n  /**\n   * Delay after window's resize finishes.\n   * @group Props\n   */\n  get resizeDelay() {\n    return this._resizeDelay;\n  }\n  set resizeDelay(val) {\n    this._resizeDelay = val;\n  }\n  /**\n   * Used to append each loaded item to top without removing any items from the DOM. Using very large data may cause the browser to crash.\n   * @group Props\n   */\n  get appendOnly() {\n    return this._appendOnly;\n  }\n  set appendOnly(val) {\n    this._appendOnly = val;\n  }\n  /**\n   * Specifies whether the scroller should be displayed inline or not.\n   * @group Props\n   */\n  get inline() {\n    return this._inline;\n  }\n  set inline(val) {\n    this._inline = val;\n  }\n  /**\n   * Defines if data is loaded and interacted with in lazy manner.\n   * @group Props\n   */\n  get lazy() {\n    return this._lazy;\n  }\n  set lazy(val) {\n    this._lazy = val;\n  }\n  /**\n   * If disabled, the scroller feature is eliminated and the content is displayed directly.\n   * @group Props\n   */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(val) {\n    this._disabled = val;\n  }\n  /**\n   * Used to implement a custom loader instead of using the loader feature in the scroller.\n   * @group Props\n   */\n  get loaderDisabled() {\n    return this._loaderDisabled;\n  }\n  set loaderDisabled(val) {\n    this._loaderDisabled = val;\n  }\n  /**\n   * Columns to display.\n   * @group Props\n   */\n  get columns() {\n    return this._columns;\n  }\n  set columns(val) {\n    this._columns = val;\n  }\n  /**\n   * Used to implement a custom spacer instead of using the spacer feature in the scroller.\n   * @group Props\n   */\n  get showSpacer() {\n    return this._showSpacer;\n  }\n  set showSpacer(val) {\n    this._showSpacer = val;\n  }\n  /**\n   * Defines whether to show loader.\n   * @group Props\n   */\n  get showLoader() {\n    return this._showLoader;\n  }\n  set showLoader(val) {\n    this._showLoader = val;\n  }\n  /**\n   * Determines how many additional elements to add to the DOM outside of the view. According to the scrolls made up and down, extra items are added in a certain algorithm in the form of multiples of this number. Default value is half the number of items shown in the view.\n   * @group Props\n   */\n  get numToleratedItems() {\n    return this._numToleratedItems;\n  }\n  set numToleratedItems(val) {\n    this._numToleratedItems = val;\n  }\n  /**\n   * Defines whether the data is loaded.\n   * @group Props\n   */\n  get loading() {\n    return this._loading;\n  }\n  set loading(val) {\n    this._loading = val;\n  }\n  /**\n   * Defines whether to dynamically change the height or width of scrollable container.\n   * @group Props\n   */\n  get autoSize() {\n    return this._autoSize;\n  }\n  set autoSize(val) {\n    this._autoSize = val;\n  }\n  /**\n   * Function to optimize the dom operations by delegating to ngForTrackBy, default algoritm checks for object identity.\n   * @group Props\n   */\n  get trackBy() {\n    return this._trackBy;\n  }\n  set trackBy(val) {\n    this._trackBy = val;\n  }\n  /**\n   * Defines whether to use the scroller feature. The properties of scroller component can be used like an object in it.\n   * @group Props\n   */\n  get options() {\n    return this._options;\n  }\n  set options(val) {\n    this._options = val;\n    if (val && typeof val === 'object') {\n      Object.entries(val).forEach(([k, v]) => this[`_${k}`] !== v && (this[`_${k}`] = v));\n      Object.entries(val).forEach(([k, v]) => this[`${k}`] !== v && (this[`${k}`] = v));\n    }\n  }\n  /**\n   * Callback to invoke in lazy mode to load new data.\n   * @param {ScrollerLazyLoadEvent} event - Custom lazy load event.\n   * @group Emits\n   */\n  onLazyLoad = new EventEmitter();\n  /**\n   * Callback to invoke when scroll position changes.\n   * @param {ScrollerScrollEvent} event - Custom scroll event.\n   * @group Emits\n   */\n  onScroll = new EventEmitter();\n  /**\n   * Callback to invoke when scroll position and item's range in view changes.\n   * @param {ScrollerScrollEvent} event - Custom scroll index change event.\n   * @group Emits\n   */\n  onScrollIndexChange = new EventEmitter();\n  elementViewChild;\n  contentViewChild;\n  height;\n  _id;\n  _style;\n  _styleClass;\n  _tabindex = 0;\n  _items;\n  _itemSize = 0;\n  _scrollHeight;\n  _scrollWidth;\n  _orientation = 'vertical';\n  _step = 0;\n  _delay = 0;\n  _resizeDelay = 10;\n  _appendOnly = false;\n  _inline = false;\n  _lazy = false;\n  _disabled = false;\n  _loaderDisabled = false;\n  _columns;\n  _showSpacer = true;\n  _showLoader = false;\n  _numToleratedItems;\n  _loading;\n  _autoSize = false;\n  _trackBy;\n  _options;\n  d_loading = false;\n  d_numToleratedItems;\n  contentEl;\n  /**\n   * Content template of the component.\n   * @group Templates\n   */\n  contentTemplate;\n  /**\n   * Item template of the component.\n   * @group Templates\n   */\n  itemTemplate;\n  /**\n   * Loader template of the component.\n   * @group Templates\n   */\n  loaderTemplate;\n  /**\n   * Loader icon template of the component.\n   * @group Templates\n   */\n  loaderIconTemplate;\n  templates;\n  _contentTemplate;\n  _itemTemplate;\n  _loaderTemplate;\n  _loaderIconTemplate;\n  first = 0;\n  last = 0;\n  page = 0;\n  isRangeChanged = false;\n  numItemsInViewport = 0;\n  lastScrollPos = 0;\n  lazyLoadState = {};\n  loaderArr = [];\n  spacerStyle = {};\n  contentStyle = {};\n  scrollTimeout;\n  resizeTimeout;\n  initialized = false;\n  windowResizeListener;\n  defaultWidth;\n  defaultHeight;\n  defaultContentWidth;\n  defaultContentHeight;\n  _contentStyleClass;\n  get contentStyleClass() {\n    return this._contentStyleClass;\n  }\n  set contentStyleClass(val) {\n    this._contentStyleClass = val;\n  }\n  get vertical() {\n    return this._orientation === 'vertical';\n  }\n  get horizontal() {\n    return this._orientation === 'horizontal';\n  }\n  get both() {\n    return this._orientation === 'both';\n  }\n  get loadedItems() {\n    if (this._items && !this.d_loading) {\n      if (this.both) return this._items.slice(this._appendOnly ? 0 : this.first.rows, this.last.rows).map(item => this._columns ? item : item.slice(this._appendOnly ? 0 : this.first.cols, this.last.cols));else if (this.horizontal && this._columns) return this._items;else return this._items.slice(this._appendOnly ? 0 : this.first, this.last);\n    }\n    return [];\n  }\n  get loadedRows() {\n    return this.d_loading ? this._loaderDisabled ? this.loaderArr : [] : this.loadedItems;\n  }\n  get loadedColumns() {\n    if (this._columns && (this.both || this.horizontal)) {\n      return this.d_loading && this._loaderDisabled ? this.both ? this.loaderArr[0] : this.loaderArr : this._columns.slice(this.both ? this.first.cols : this.first, this.both ? this.last.cols : this.last);\n    }\n    return this._columns;\n  }\n  _componentStyle = inject(ScrollerStyle);\n  constructor(zone) {\n    super();\n    this.zone = zone;\n  }\n  ngOnInit() {\n    super.ngOnInit();\n    this.setInitialState();\n  }\n  ngOnChanges(simpleChanges) {\n    super.ngOnChanges(simpleChanges);\n    let isLoadingChanged = false;\n    if (this.scrollHeight == '100%') {\n      this.height = '100%';\n    }\n    if (simpleChanges.loading) {\n      const {\n        previousValue,\n        currentValue\n      } = simpleChanges.loading;\n      if (this.lazy && previousValue !== currentValue && currentValue !== this.d_loading) {\n        this.d_loading = currentValue;\n        isLoadingChanged = true;\n      }\n    }\n    if (simpleChanges.orientation) {\n      this.lastScrollPos = this.both ? {\n        top: 0,\n        left: 0\n      } : 0;\n    }\n    if (simpleChanges.numToleratedItems) {\n      const {\n        previousValue,\n        currentValue\n      } = simpleChanges.numToleratedItems;\n      if (previousValue !== currentValue && currentValue !== this.d_numToleratedItems) {\n        this.d_numToleratedItems = currentValue;\n      }\n    }\n    if (simpleChanges.options) {\n      const {\n        previousValue,\n        currentValue\n      } = simpleChanges.options;\n      if (this.lazy && previousValue?.loading !== currentValue?.loading && currentValue?.loading !== this.d_loading) {\n        this.d_loading = currentValue.loading;\n        isLoadingChanged = true;\n      }\n      if (previousValue?.numToleratedItems !== currentValue?.numToleratedItems && currentValue?.numToleratedItems !== this.d_numToleratedItems) {\n        this.d_numToleratedItems = currentValue.numToleratedItems;\n      }\n    }\n    if (this.initialized) {\n      const isChanged = !isLoadingChanged && (simpleChanges.items?.previousValue?.length !== simpleChanges.items?.currentValue?.length || simpleChanges.itemSize || simpleChanges.scrollHeight || simpleChanges.scrollWidth);\n      if (isChanged) {\n        this.init();\n        this.calculateAutoSize();\n      }\n    }\n  }\n  ngAfterContentInit() {\n    this.templates.forEach(item => {\n      switch (item.getType()) {\n        case 'content':\n          this._contentTemplate = item.template;\n          break;\n        case 'item':\n          this._itemTemplate = item.template;\n          break;\n        case 'loader':\n          this._loaderTemplate = item.template;\n          break;\n        case 'loadericon':\n          this._loaderIconTemplate = item.template;\n          break;\n        default:\n          this._itemTemplate = item.template;\n          break;\n      }\n    });\n  }\n  ngAfterViewInit() {\n    super.ngAfterViewInit();\n    Promise.resolve().then(() => {\n      this.viewInit();\n    });\n  }\n  ngAfterViewChecked() {\n    if (!this.initialized) {\n      this.viewInit();\n    }\n  }\n  ngOnDestroy() {\n    this.unbindResizeListener();\n    this.contentEl = null;\n    this.initialized = false;\n    super.ngOnDestroy();\n  }\n  viewInit() {\n    if (isPlatformBrowser(this.platformId) && !this.initialized) {\n      if (isVisible(this.elementViewChild?.nativeElement)) {\n        this.setInitialState();\n        this.setContentEl(this.contentEl);\n        this.init();\n        this.defaultWidth = getWidth(this.elementViewChild?.nativeElement);\n        this.defaultHeight = getHeight(this.elementViewChild?.nativeElement);\n        this.defaultContentWidth = getWidth(this.contentEl);\n        this.defaultContentHeight = getHeight(this.contentEl);\n        this.initialized = true;\n      }\n    }\n  }\n  init() {\n    if (!this._disabled) {\n      this.setSize();\n      this.calculateOptions();\n      this.setSpacerSize();\n      this.bindResizeListener();\n      this.cd.detectChanges();\n    }\n  }\n  setContentEl(el) {\n    this.contentEl = el || this.contentViewChild?.nativeElement || findSingle(this.elementViewChild?.nativeElement, '.p-virtualscroller-content');\n  }\n  setInitialState() {\n    this.first = this.both ? {\n      rows: 0,\n      cols: 0\n    } : 0;\n    this.last = this.both ? {\n      rows: 0,\n      cols: 0\n    } : 0;\n    this.numItemsInViewport = this.both ? {\n      rows: 0,\n      cols: 0\n    } : 0;\n    this.lastScrollPos = this.both ? {\n      top: 0,\n      left: 0\n    } : 0;\n    this.d_loading = this._loading || false;\n    this.d_numToleratedItems = this._numToleratedItems;\n    this.loaderArr = [];\n  }\n  getElementRef() {\n    return this.elementViewChild;\n  }\n  getPageByFirst(first) {\n    return Math.floor(((first ?? this.first) + this.d_numToleratedItems * 4) / (this._step || 1));\n  }\n  isPageChanged(first) {\n    return this._step ? this.page !== this.getPageByFirst(first ?? this.first) : true;\n  }\n  scrollTo(options) {\n    // this.lastScrollPos = this.both ? { top: 0, left: 0 } : 0;\n    this.elementViewChild?.nativeElement?.scrollTo(options);\n  }\n  scrollToIndex(index, behavior = 'auto') {\n    const valid = this.both ? index.every(i => i > -1) : index > -1;\n    if (valid) {\n      const first = this.first;\n      const {\n        scrollTop = 0,\n        scrollLeft = 0\n      } = this.elementViewChild?.nativeElement;\n      const {\n        numToleratedItems\n      } = this.calculateNumItems();\n      const contentPos = this.getContentPosition();\n      const itemSize = this.itemSize;\n      const calculateFirst = (_index = 0, _numT) => _index <= _numT ? 0 : _index;\n      const calculateCoord = (_first, _size, _cpos) => _first * _size + _cpos;\n      const scrollTo = (left = 0, top = 0) => this.scrollTo({\n        left,\n        top,\n        behavior\n      });\n      let newFirst = this.both ? {\n        rows: 0,\n        cols: 0\n      } : 0;\n      let isRangeChanged = false,\n        isScrollChanged = false;\n      if (this.both) {\n        newFirst = {\n          rows: calculateFirst(index[0], numToleratedItems[0]),\n          cols: calculateFirst(index[1], numToleratedItems[1])\n        };\n        scrollTo(calculateCoord(newFirst.cols, itemSize[1], contentPos.left), calculateCoord(newFirst.rows, itemSize[0], contentPos.top));\n        isScrollChanged = this.lastScrollPos.top !== scrollTop || this.lastScrollPos.left !== scrollLeft;\n        isRangeChanged = newFirst.rows !== first.rows || newFirst.cols !== first.cols;\n      } else {\n        newFirst = calculateFirst(index, numToleratedItems);\n        this.horizontal ? scrollTo(calculateCoord(newFirst, itemSize, contentPos.left), scrollTop) : scrollTo(scrollLeft, calculateCoord(newFirst, itemSize, contentPos.top));\n        isScrollChanged = this.lastScrollPos !== (this.horizontal ? scrollLeft : scrollTop);\n        isRangeChanged = newFirst !== first;\n      }\n      this.isRangeChanged = isRangeChanged;\n      isScrollChanged && (this.first = newFirst);\n    }\n  }\n  scrollInView(index, to, behavior = 'auto') {\n    if (to) {\n      const {\n        first,\n        viewport\n      } = this.getRenderedRange();\n      const scrollTo = (left = 0, top = 0) => this.scrollTo({\n        left,\n        top,\n        behavior\n      });\n      const isToStart = to === 'to-start';\n      const isToEnd = to === 'to-end';\n      if (isToStart) {\n        if (this.both) {\n          if (viewport.first.rows - first.rows > index[0]) {\n            scrollTo(viewport.first.cols * this._itemSize[1], (viewport.first.rows - 1) * this._itemSize[0]);\n          } else if (viewport.first.cols - first.cols > index[1]) {\n            scrollTo((viewport.first.cols - 1) * this._itemSize[1], viewport.first.rows * this._itemSize[0]);\n          }\n        } else {\n          if (viewport.first - first > index) {\n            const pos = (viewport.first - 1) * this._itemSize;\n            this.horizontal ? scrollTo(pos, 0) : scrollTo(0, pos);\n          }\n        }\n      } else if (isToEnd) {\n        if (this.both) {\n          if (viewport.last.rows - first.rows <= index[0] + 1) {\n            scrollTo(viewport.first.cols * this._itemSize[1], (viewport.first.rows + 1) * this._itemSize[0]);\n          } else if (viewport.last.cols - first.cols <= index[1] + 1) {\n            scrollTo((viewport.first.cols + 1) * this._itemSize[1], viewport.first.rows * this._itemSize[0]);\n          }\n        } else {\n          if (viewport.last - first <= index + 1) {\n            const pos = (viewport.first + 1) * this._itemSize;\n            this.horizontal ? scrollTo(pos, 0) : scrollTo(0, pos);\n          }\n        }\n      }\n    } else {\n      this.scrollToIndex(index, behavior);\n    }\n  }\n  getRenderedRange() {\n    const calculateFirstInViewport = (_pos, _size) => _size || _pos ? Math.floor(_pos / (_size || _pos)) : 0;\n    let firstInViewport = this.first;\n    let lastInViewport = 0;\n    if (this.elementViewChild?.nativeElement) {\n      const {\n        scrollTop,\n        scrollLeft\n      } = this.elementViewChild.nativeElement;\n      if (this.both) {\n        firstInViewport = {\n          rows: calculateFirstInViewport(scrollTop, this._itemSize[0]),\n          cols: calculateFirstInViewport(scrollLeft, this._itemSize[1])\n        };\n        lastInViewport = {\n          rows: firstInViewport.rows + this.numItemsInViewport.rows,\n          cols: firstInViewport.cols + this.numItemsInViewport.cols\n        };\n      } else {\n        const scrollPos = this.horizontal ? scrollLeft : scrollTop;\n        firstInViewport = calculateFirstInViewport(scrollPos, this._itemSize);\n        lastInViewport = firstInViewport + this.numItemsInViewport;\n      }\n    }\n    return {\n      first: this.first,\n      last: this.last,\n      viewport: {\n        first: firstInViewport,\n        last: lastInViewport\n      }\n    };\n  }\n  calculateNumItems() {\n    const contentPos = this.getContentPosition();\n    const contentWidth = (this.elementViewChild?.nativeElement ? this.elementViewChild.nativeElement.offsetWidth - contentPos.left : 0) || 0;\n    const contentHeight = (this.elementViewChild?.nativeElement ? this.elementViewChild.nativeElement.offsetHeight - contentPos.top : 0) || 0;\n    const calculateNumItemsInViewport = (_contentSize, _itemSize) => _itemSize || _contentSize ? Math.ceil(_contentSize / (_itemSize || _contentSize)) : 0;\n    const calculateNumToleratedItems = _numItems => Math.ceil(_numItems / 2);\n    const numItemsInViewport = this.both ? {\n      rows: calculateNumItemsInViewport(contentHeight, this._itemSize[0]),\n      cols: calculateNumItemsInViewport(contentWidth, this._itemSize[1])\n    } : calculateNumItemsInViewport(this.horizontal ? contentWidth : contentHeight, this._itemSize);\n    const numToleratedItems = this.d_numToleratedItems || (this.both ? [calculateNumToleratedItems(numItemsInViewport.rows), calculateNumToleratedItems(numItemsInViewport.cols)] : calculateNumToleratedItems(numItemsInViewport));\n    return {\n      numItemsInViewport,\n      numToleratedItems\n    };\n  }\n  calculateOptions() {\n    const {\n      numItemsInViewport,\n      numToleratedItems\n    } = this.calculateNumItems();\n    const calculateLast = (_first, _num, _numT, _isCols = false) => this.getLast(_first + _num + (_first < _numT ? 2 : 3) * _numT, _isCols);\n    const first = this.first;\n    const last = this.both ? {\n      rows: calculateLast(this.first.rows, numItemsInViewport.rows, numToleratedItems[0]),\n      cols: calculateLast(this.first.cols, numItemsInViewport.cols, numToleratedItems[1], true)\n    } : calculateLast(this.first, numItemsInViewport, numToleratedItems);\n    this.last = last;\n    this.numItemsInViewport = numItemsInViewport;\n    this.d_numToleratedItems = numToleratedItems;\n    if (this.showLoader) {\n      this.loaderArr = this.both ? Array.from({\n        length: numItemsInViewport.rows\n      }).map(() => Array.from({\n        length: numItemsInViewport.cols\n      })) : Array.from({\n        length: numItemsInViewport\n      });\n    }\n    if (this._lazy) {\n      Promise.resolve().then(() => {\n        this.lazyLoadState = {\n          first: this._step ? this.both ? {\n            rows: 0,\n            cols: first.cols\n          } : 0 : first,\n          last: Math.min(this._step ? this._step : this.last, this.items.length)\n        };\n        this.handleEvents('onLazyLoad', this.lazyLoadState);\n      });\n    }\n  }\n  calculateAutoSize() {\n    if (this._autoSize && !this.d_loading) {\n      Promise.resolve().then(() => {\n        if (this.contentEl) {\n          this.contentEl.style.minHeight = this.contentEl.style.minWidth = 'auto';\n          this.contentEl.style.position = 'relative';\n          this.elementViewChild.nativeElement.style.contain = 'none';\n          const [contentWidth, contentHeight] = [getWidth(this.contentEl), getHeight(this.contentEl)];\n          contentWidth !== this.defaultContentWidth && (this.elementViewChild.nativeElement.style.width = '');\n          contentHeight !== this.defaultContentHeight && (this.elementViewChild.nativeElement.style.height = '');\n          const [width, height] = [getWidth(this.elementViewChild.nativeElement), getHeight(this.elementViewChild.nativeElement)];\n          (this.both || this.horizontal) && (this.elementViewChild.nativeElement.style.width = width < this.defaultWidth ? width + 'px' : this._scrollWidth || this.defaultWidth + 'px');\n          (this.both || this.vertical) && (this.elementViewChild.nativeElement.style.height = height < this.defaultHeight ? height + 'px' : this._scrollHeight || this.defaultHeight + 'px');\n          this.contentEl.style.minHeight = this.contentEl.style.minWidth = '';\n          this.contentEl.style.position = '';\n          this.elementViewChild.nativeElement.style.contain = '';\n        }\n      });\n    }\n  }\n  getLast(last = 0, isCols = false) {\n    return this._items ? Math.min(isCols ? (this._columns || this._items[0]).length : this._items.length, last) : 0;\n  }\n  getContentPosition() {\n    if (this.contentEl) {\n      const style = getComputedStyle(this.contentEl);\n      const left = parseFloat(style.paddingLeft) + Math.max(parseFloat(style.left) || 0, 0);\n      const right = parseFloat(style.paddingRight) + Math.max(parseFloat(style.right) || 0, 0);\n      const top = parseFloat(style.paddingTop) + Math.max(parseFloat(style.top) || 0, 0);\n      const bottom = parseFloat(style.paddingBottom) + Math.max(parseFloat(style.bottom) || 0, 0);\n      return {\n        left,\n        right,\n        top,\n        bottom,\n        x: left + right,\n        y: top + bottom\n      };\n    }\n    return {\n      left: 0,\n      right: 0,\n      top: 0,\n      bottom: 0,\n      x: 0,\n      y: 0\n    };\n  }\n  setSize() {\n    if (this.elementViewChild?.nativeElement) {\n      const parentElement = this.elementViewChild.nativeElement.parentElement.parentElement;\n      const width = this._scrollWidth || `${this.elementViewChild.nativeElement.offsetWidth || parentElement.offsetWidth}px`;\n      const height = this._scrollHeight || `${this.elementViewChild.nativeElement.offsetHeight || parentElement.offsetHeight}px`;\n      const setProp = (_name, _value) => this.elementViewChild.nativeElement.style[_name] = _value;\n      if (this.both || this.horizontal) {\n        setProp('height', height);\n        setProp('width', width);\n      } else {\n        setProp('height', height);\n      }\n    }\n  }\n  setSpacerSize() {\n    if (this._items) {\n      const contentPos = this.getContentPosition();\n      const setProp = (_name, _value, _size, _cpos = 0) => this.spacerStyle = {\n        ...this.spacerStyle,\n        ...{\n          [`${_name}`]: (_value || []).length * _size + _cpos + 'px'\n        }\n      };\n      if (this.both) {\n        setProp('height', this._items, this._itemSize[0], contentPos.y);\n        setProp('width', this._columns || this._items[1], this._itemSize[1], contentPos.x);\n      } else {\n        this.horizontal ? setProp('width', this._columns || this._items, this._itemSize, contentPos.x) : setProp('height', this._items, this._itemSize, contentPos.y);\n      }\n    }\n  }\n  setContentPosition(pos) {\n    if (this.contentEl && !this._appendOnly) {\n      const first = pos ? pos.first : this.first;\n      const calculateTranslateVal = (_first, _size) => _first * _size;\n      const setTransform = (_x = 0, _y = 0) => this.contentStyle = {\n        ...this.contentStyle,\n        ...{\n          transform: `translate3d(${_x}px, ${_y}px, 0)`\n        }\n      };\n      if (this.both) {\n        setTransform(calculateTranslateVal(first.cols, this._itemSize[1]), calculateTranslateVal(first.rows, this._itemSize[0]));\n      } else {\n        const translateVal = calculateTranslateVal(first, this._itemSize);\n        this.horizontal ? setTransform(translateVal, 0) : setTransform(0, translateVal);\n      }\n    }\n  }\n  onScrollPositionChange(event) {\n    const target = event.target;\n    const contentPos = this.getContentPosition();\n    const calculateScrollPos = (_pos, _cpos) => _pos ? _pos > _cpos ? _pos - _cpos : _pos : 0;\n    const calculateCurrentIndex = (_pos, _size) => _size || _pos ? Math.floor(_pos / (_size || _pos)) : 0;\n    const calculateTriggerIndex = (_currentIndex, _first, _last, _num, _numT, _isScrollDownOrRight) => {\n      return _currentIndex <= _numT ? _numT : _isScrollDownOrRight ? _last - _num - _numT : _first + _numT - 1;\n    };\n    const calculateFirst = (_currentIndex, _triggerIndex, _first, _last, _num, _numT, _isScrollDownOrRight) => {\n      if (_currentIndex <= _numT) return 0;else return Math.max(0, _isScrollDownOrRight ? _currentIndex < _triggerIndex ? _first : _currentIndex - _numT : _currentIndex > _triggerIndex ? _first : _currentIndex - 2 * _numT);\n    };\n    const calculateLast = (_currentIndex, _first, _last, _num, _numT, _isCols = false) => {\n      let lastValue = _first + _num + 2 * _numT;\n      if (_currentIndex >= _numT) {\n        lastValue += _numT + 1;\n      }\n      return this.getLast(lastValue, _isCols);\n    };\n    const scrollTop = calculateScrollPos(target.scrollTop, contentPos.top);\n    const scrollLeft = calculateScrollPos(target.scrollLeft, contentPos.left);\n    let newFirst = this.both ? {\n      rows: 0,\n      cols: 0\n    } : 0;\n    let newLast = this.last;\n    let isRangeChanged = false;\n    let newScrollPos = this.lastScrollPos;\n    if (this.both) {\n      const isScrollDown = this.lastScrollPos.top <= scrollTop;\n      const isScrollRight = this.lastScrollPos.left <= scrollLeft;\n      if (!this._appendOnly || this._appendOnly && (isScrollDown || isScrollRight)) {\n        const currentIndex = {\n          rows: calculateCurrentIndex(scrollTop, this._itemSize[0]),\n          cols: calculateCurrentIndex(scrollLeft, this._itemSize[1])\n        };\n        const triggerIndex = {\n          rows: calculateTriggerIndex(currentIndex.rows, this.first.rows, this.last.rows, this.numItemsInViewport.rows, this.d_numToleratedItems[0], isScrollDown),\n          cols: calculateTriggerIndex(currentIndex.cols, this.first.cols, this.last.cols, this.numItemsInViewport.cols, this.d_numToleratedItems[1], isScrollRight)\n        };\n        newFirst = {\n          rows: calculateFirst(currentIndex.rows, triggerIndex.rows, this.first.rows, this.last.rows, this.numItemsInViewport.rows, this.d_numToleratedItems[0], isScrollDown),\n          cols: calculateFirst(currentIndex.cols, triggerIndex.cols, this.first.cols, this.last.cols, this.numItemsInViewport.cols, this.d_numToleratedItems[1], isScrollRight)\n        };\n        newLast = {\n          rows: calculateLast(currentIndex.rows, newFirst.rows, this.last.rows, this.numItemsInViewport.rows, this.d_numToleratedItems[0]),\n          cols: calculateLast(currentIndex.cols, newFirst.cols, this.last.cols, this.numItemsInViewport.cols, this.d_numToleratedItems[1], true)\n        };\n        isRangeChanged = newFirst.rows !== this.first.rows || newLast.rows !== this.last.rows || newFirst.cols !== this.first.cols || newLast.cols !== this.last.cols || this.isRangeChanged;\n        newScrollPos = {\n          top: scrollTop,\n          left: scrollLeft\n        };\n      }\n    } else {\n      const scrollPos = this.horizontal ? scrollLeft : scrollTop;\n      const isScrollDownOrRight = this.lastScrollPos <= scrollPos;\n      if (!this._appendOnly || this._appendOnly && isScrollDownOrRight) {\n        const currentIndex = calculateCurrentIndex(scrollPos, this._itemSize);\n        const triggerIndex = calculateTriggerIndex(currentIndex, this.first, this.last, this.numItemsInViewport, this.d_numToleratedItems, isScrollDownOrRight);\n        newFirst = calculateFirst(currentIndex, triggerIndex, this.first, this.last, this.numItemsInViewport, this.d_numToleratedItems, isScrollDownOrRight);\n        newLast = calculateLast(currentIndex, newFirst, this.last, this.numItemsInViewport, this.d_numToleratedItems);\n        isRangeChanged = newFirst !== this.first || newLast !== this.last || this.isRangeChanged;\n        newScrollPos = scrollPos;\n      }\n    }\n    return {\n      first: newFirst,\n      last: newLast,\n      isRangeChanged,\n      scrollPos: newScrollPos\n    };\n  }\n  onScrollChange(event) {\n    const {\n      first,\n      last,\n      isRangeChanged,\n      scrollPos\n    } = this.onScrollPositionChange(event);\n    if (isRangeChanged) {\n      const newState = {\n        first,\n        last\n      };\n      this.setContentPosition(newState);\n      this.first = first;\n      this.last = last;\n      this.lastScrollPos = scrollPos;\n      this.handleEvents('onScrollIndexChange', newState);\n      if (this._lazy && this.isPageChanged(first)) {\n        const lazyLoadState = {\n          first: this._step ? Math.min(this.getPageByFirst(first) * this._step, this.items.length - this._step) : first,\n          last: Math.min(this._step ? (this.getPageByFirst(first) + 1) * this._step : last, this.items.length)\n        };\n        const isLazyStateChanged = this.lazyLoadState.first !== lazyLoadState.first || this.lazyLoadState.last !== lazyLoadState.last;\n        isLazyStateChanged && this.handleEvents('onLazyLoad', lazyLoadState);\n        this.lazyLoadState = lazyLoadState;\n      }\n    }\n  }\n  onContainerScroll(event) {\n    this.handleEvents('onScroll', {\n      originalEvent: event\n    });\n    if (this._delay && this.isPageChanged()) {\n      if (this.scrollTimeout) {\n        clearTimeout(this.scrollTimeout);\n      }\n      if (!this.d_loading && this.showLoader) {\n        const {\n          isRangeChanged\n        } = this.onScrollPositionChange(event);\n        const changed = isRangeChanged || (this._step ? this.isPageChanged() : false);\n        if (changed) {\n          this.d_loading = true;\n          this.cd.detectChanges();\n        }\n      }\n      this.scrollTimeout = setTimeout(() => {\n        this.onScrollChange(event);\n        if (this.d_loading && this.showLoader && (!this._lazy || this._loading === undefined)) {\n          this.d_loading = false;\n          this.page = this.getPageByFirst();\n        }\n        this.cd.detectChanges();\n      }, this._delay);\n    } else {\n      !this.d_loading && this.onScrollChange(event);\n    }\n  }\n  bindResizeListener() {\n    if (isPlatformBrowser(this.platformId)) {\n      if (!this.windowResizeListener) {\n        this.zone.runOutsideAngular(() => {\n          const window = this.document.defaultView;\n          const event = isTouchDevice() ? 'orientationchange' : 'resize';\n          this.windowResizeListener = this.renderer.listen(window, event, this.onWindowResize.bind(this));\n        });\n      }\n    }\n  }\n  unbindResizeListener() {\n    if (this.windowResizeListener) {\n      this.windowResizeListener();\n      this.windowResizeListener = null;\n    }\n  }\n  onWindowResize() {\n    if (this.resizeTimeout) {\n      clearTimeout(this.resizeTimeout);\n    }\n    this.resizeTimeout = setTimeout(() => {\n      if (isVisible(this.elementViewChild?.nativeElement)) {\n        const [width, height] = [getWidth(this.elementViewChild?.nativeElement), getHeight(this.elementViewChild?.nativeElement)];\n        const [isDiffWidth, isDiffHeight] = [width !== this.defaultWidth, height !== this.defaultHeight];\n        const reinit = this.both ? isDiffWidth || isDiffHeight : this.horizontal ? isDiffWidth : this.vertical ? isDiffHeight : false;\n        reinit && this.zone.run(() => {\n          this.d_numToleratedItems = this._numToleratedItems;\n          this.defaultWidth = width;\n          this.defaultHeight = height;\n          this.defaultContentWidth = getWidth(this.contentEl);\n          this.defaultContentHeight = getHeight(this.contentEl);\n          this.init();\n        });\n      }\n    }, this._resizeDelay);\n  }\n  handleEvents(name, params) {\n    //@ts-ignore\n    return this.options && this.options[name] ? this.options[name](params) : this[name].emit(params);\n  }\n  getContentOptions() {\n    return {\n      contentStyleClass: `p-virtualscroller-content ${this.d_loading ? 'p-virtualscroller-loading' : ''}`,\n      items: this.loadedItems,\n      getItemOptions: index => this.getOptions(index),\n      loading: this.d_loading,\n      getLoaderOptions: (index, options) => this.getLoaderOptions(index, options),\n      itemSize: this._itemSize,\n      rows: this.loadedRows,\n      columns: this.loadedColumns,\n      spacerStyle: this.spacerStyle,\n      contentStyle: this.contentStyle,\n      vertical: this.vertical,\n      horizontal: this.horizontal,\n      both: this.both\n    };\n  }\n  getOptions(renderedIndex) {\n    const count = (this._items || []).length;\n    const index = this.both ? this.first.rows + renderedIndex : this.first + renderedIndex;\n    return {\n      index,\n      count,\n      first: index === 0,\n      last: index === count - 1,\n      even: index % 2 === 0,\n      odd: index % 2 !== 0\n    };\n  }\n  getLoaderOptions(index, extOptions) {\n    const count = this.loaderArr.length;\n    return {\n      index,\n      count,\n      first: index === 0,\n      last: index === count - 1,\n      even: index % 2 === 0,\n      odd: index % 2 !== 0,\n      ...extOptions\n    };\n  }\n  static ɵfac = function Scroller_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || Scroller)(i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: Scroller,\n    selectors: [[\"p-scroller\"], [\"p-virtualscroller\"], [\"p-virtual-scroller\"], [\"p-virtualScroller\"]],\n    contentQueries: function Scroller_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, _c0, 4);\n        i0.ɵɵcontentQuery(dirIndex, _c1, 4);\n        i0.ɵɵcontentQuery(dirIndex, _c2, 4);\n        i0.ɵɵcontentQuery(dirIndex, _c3, 4);\n        i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.contentTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.itemTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.loaderTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.loaderIconTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n      }\n    },\n    viewQuery: function Scroller_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c4, 5);\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.elementViewChild = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.contentViewChild = _t.first);\n      }\n    },\n    hostVars: 2,\n    hostBindings: function Scroller_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵstyleProp(\"height\", ctx.height);\n      }\n    },\n    inputs: {\n      id: \"id\",\n      style: \"style\",\n      styleClass: \"styleClass\",\n      tabindex: \"tabindex\",\n      items: \"items\",\n      itemSize: \"itemSize\",\n      scrollHeight: \"scrollHeight\",\n      scrollWidth: \"scrollWidth\",\n      orientation: \"orientation\",\n      step: \"step\",\n      delay: \"delay\",\n      resizeDelay: \"resizeDelay\",\n      appendOnly: \"appendOnly\",\n      inline: \"inline\",\n      lazy: \"lazy\",\n      disabled: \"disabled\",\n      loaderDisabled: \"loaderDisabled\",\n      columns: \"columns\",\n      showSpacer: \"showSpacer\",\n      showLoader: \"showLoader\",\n      numToleratedItems: \"numToleratedItems\",\n      loading: \"loading\",\n      autoSize: \"autoSize\",\n      trackBy: \"trackBy\",\n      options: \"options\"\n    },\n    outputs: {\n      onLazyLoad: \"onLazyLoad\",\n      onScroll: \"onScroll\",\n      onScrollIndexChange: \"onScrollIndexChange\"\n    },\n    features: [i0.ɵɵProvidersFeature([ScrollerStyle]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature],\n    ngContentSelectors: _c5,\n    decls: 3,\n    vars: 2,\n    consts: [[\"disabledContainer\", \"\"], [\"element\", \"\"], [\"buildInContent\", \"\"], [\"content\", \"\"], [\"buildInLoader\", \"\"], [\"buildInLoaderIcon\", \"\"], [4, \"ngIf\", \"ngIfElse\"], [3, \"scroll\", \"ngStyle\", \"ngClass\"], [\"class\", \"p-virtualscroller-spacer\", 3, \"ngStyle\", 4, \"ngIf\"], [\"class\", \"p-virtualscroller-loader\", 3, \"ngClass\", 4, \"ngIf\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [3, \"ngClass\"], [4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"p-virtualscroller-spacer\", 3, \"ngStyle\"], [1, \"p-virtualscroller-loader\", 3, \"ngClass\"], [4, \"ngFor\", \"ngForOf\"], [3, \"styleClass\"], [4, \"ngIf\"]],\n    template: function Scroller_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵtemplate(0, Scroller_ng_container_0_Template, 8, 16, \"ng-container\", 6)(1, Scroller_ng_template_1_Template, 2, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n      }\n      if (rf & 2) {\n        const disabledContainer_r9 = i0.ɵɵreference(2);\n        i0.ɵɵproperty(\"ngIf\", !ctx._disabled)(\"ngIfElse\", disabledContainer_r9);\n      }\n    },\n    dependencies: [CommonModule, i1.NgClass, i1.NgForOf, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, SpinnerIcon, SharedModule],\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Scroller, [{\n    type: Component,\n    args: [{\n      selector: 'p-scroller, p-virtualscroller, p-virtual-scroller, p-virtualScroller',\n      imports: [CommonModule, SpinnerIcon, SharedModule],\n      standalone: true,\n      template: `\n        <ng-container *ngIf=\"!_disabled; else disabledContainer\">\n            <div\n                #element\n                [attr.id]=\"_id\"\n                [attr.tabindex]=\"tabindex\"\n                [ngStyle]=\"_style\"\n                [class]=\"_styleClass\"\n                [ngClass]=\"{\n                    'p-virtualscroller': true,\n                    'p-virtualscroller-inline': inline,\n                    'p-virtualscroller-both p-both-scroll': both,\n                    'p-virtualscroller-horizontal p-horizontal-scroll': horizontal\n                }\"\n                (scroll)=\"onContainerScroll($event)\"\n                [attr.data-pc-name]=\"'scroller'\"\n                [attr.data-pc-section]=\"'root'\"\n            >\n                <ng-container *ngIf=\"contentTemplate || _contentTemplate; else buildInContent\">\n                    <ng-container *ngTemplateOutlet=\"contentTemplate || _contentTemplate; context: { $implicit: loadedItems, options: getContentOptions() }\"></ng-container>\n                </ng-container>\n                <ng-template #buildInContent>\n                    <div #content [class]=\"contentStyleClass\" [ngClass]=\"{ 'p-virtualscroller-content': true, 'p-virtualscroller-loading ': d_loading }\" [style]=\"contentStyle\" [attr.data-pc-section]=\"'content'\">\n                        <ng-container *ngFor=\"let item of loadedItems; let index = index; trackBy: _trackBy\">\n                            <ng-container *ngTemplateOutlet=\"itemTemplate || _itemTemplate; context: { $implicit: item, options: getOptions(index) }\"></ng-container>\n                        </ng-container>\n                    </div>\n                </ng-template>\n                <div *ngIf=\"_showSpacer\" class=\"p-virtualscroller-spacer\" [ngStyle]=\"spacerStyle\" [attr.data-pc-section]=\"'spacer'\"></div>\n                <div *ngIf=\"!loaderDisabled && _showLoader && d_loading\" class=\"p-virtualscroller-loader\" [ngClass]=\"{ 'p-virtualscroller-loader-mask': !loaderTemplate }\" [attr.data-pc-section]=\"'loader'\">\n                    <ng-container *ngIf=\"loaderTemplate || _loaderTemplate; else buildInLoader\">\n                        <ng-container *ngFor=\"let item of loaderArr; let index = index\">\n                            <ng-container\n                                *ngTemplateOutlet=\"\n                                    loaderTemplate || _loaderTemplate;\n                                    context: {\n                                        options: getLoaderOptions(index, both && { numCols: numItemsInViewport.cols })\n                                    }\n                                \"\n                            ></ng-container>\n                        </ng-container>\n                    </ng-container>\n                    <ng-template #buildInLoader>\n                        <ng-container *ngIf=\"loaderIconTemplate || _loaderIconTemplate; else buildInLoaderIcon\">\n                            <ng-container *ngTemplateOutlet=\"loaderIconTemplate || _loaderIconTemplate; context: { options: { styleClass: 'p-virtualscroller-loading-icon' } }\"></ng-container>\n                        </ng-container>\n                        <ng-template #buildInLoaderIcon>\n                            <SpinnerIcon [styleClass]=\"'p-virtualscroller-loading-icon pi-spin'\" [attr.data-pc-section]=\"'loadingIcon'\" />\n                        </ng-template>\n                    </ng-template>\n                </div>\n            </div>\n        </ng-container>\n        <ng-template #disabledContainer>\n            <ng-content></ng-content>\n            <ng-container *ngIf=\"contentTemplate || _contentTemplate\">\n                <ng-container *ngTemplateOutlet=\"contentTemplate || _contentTemplate; context: { $implicit: items, options: { rows: _items, columns: loadedColumns } }\"></ng-container>\n            </ng-container>\n        </ng-template>\n    `,\n      changeDetection: ChangeDetectionStrategy.Default,\n      encapsulation: ViewEncapsulation.None,\n      providers: [ScrollerStyle]\n    }]\n  }], () => [{\n    type: i0.NgZone\n  }], {\n    id: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    tabindex: [{\n      type: Input\n    }],\n    items: [{\n      type: Input\n    }],\n    itemSize: [{\n      type: Input\n    }],\n    scrollHeight: [{\n      type: Input\n    }],\n    scrollWidth: [{\n      type: Input\n    }],\n    orientation: [{\n      type: Input\n    }],\n    step: [{\n      type: Input\n    }],\n    delay: [{\n      type: Input\n    }],\n    resizeDelay: [{\n      type: Input\n    }],\n    appendOnly: [{\n      type: Input\n    }],\n    inline: [{\n      type: Input\n    }],\n    lazy: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    loaderDisabled: [{\n      type: Input\n    }],\n    columns: [{\n      type: Input\n    }],\n    showSpacer: [{\n      type: Input\n    }],\n    showLoader: [{\n      type: Input\n    }],\n    numToleratedItems: [{\n      type: Input\n    }],\n    loading: [{\n      type: Input\n    }],\n    autoSize: [{\n      type: Input\n    }],\n    trackBy: [{\n      type: Input\n    }],\n    options: [{\n      type: Input\n    }],\n    onLazyLoad: [{\n      type: Output\n    }],\n    onScroll: [{\n      type: Output\n    }],\n    onScrollIndexChange: [{\n      type: Output\n    }],\n    elementViewChild: [{\n      type: ViewChild,\n      args: ['element']\n    }],\n    contentViewChild: [{\n      type: ViewChild,\n      args: ['content']\n    }],\n    height: [{\n      type: HostBinding,\n      args: ['style.height']\n    }],\n    contentTemplate: [{\n      type: ContentChild,\n      args: ['content', {\n        descendants: false\n      }]\n    }],\n    itemTemplate: [{\n      type: ContentChild,\n      args: ['item', {\n        descendants: false\n      }]\n    }],\n    loaderTemplate: [{\n      type: ContentChild,\n      args: ['loader', {\n        descendants: false\n      }]\n    }],\n    loaderIconTemplate: [{\n      type: ContentChild,\n      args: ['loadericon', {\n        descendants: false\n      }]\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }]\n  });\n})();\nclass ScrollerModule {\n  static ɵfac = function ScrollerModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ScrollerModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: ScrollerModule,\n    imports: [Scroller, SharedModule],\n    exports: [Scroller, SharedModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [Scroller, SharedModule, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ScrollerModule, [{\n    type: NgModule,\n    args: [{\n      imports: [Scroller, SharedModule],\n      exports: [Scroller, SharedModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Scroller, ScrollerClasses, ScrollerModule, ScrollerStyle };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA,IAAM,MAAM,CAAC,SAAS;AACtB,IAAM,MAAM,CAAC,MAAM;AACnB,IAAM,MAAM,CAAC,QAAQ;AACrB,IAAM,MAAM,CAAC,YAAY;AACzB,IAAM,MAAM,CAAC,SAAS;AACtB,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,MAAM,CAAC,IAAI,IAAI,QAAQ;AAAA,EAC3B,qBAAqB;AAAA,EACrB,4BAA4B;AAAA,EAC5B,wCAAwC;AAAA,EACxC,oDAAoD;AACtD;AACA,IAAM,MAAM,CAAC,IAAI,QAAQ;AAAA,EACvB,WAAW;AAAA,EACX,SAAS;AACX;AACA,IAAM,MAAM,SAAO;AAAA,EACjB,6BAA6B;AAAA,EAC7B,8BAA8B;AAChC;AACA,IAAM,MAAM,SAAO;AAAA,EACjB,iCAAiC;AACnC;AACA,IAAM,OAAO,SAAO;AAAA,EAClB,SAAS;AACX;AACA,IAAM,OAAO,SAAO;AAAA,EAClB,SAAS;AACX;AACA,IAAM,OAAO,OAAO;AAAA,EAClB,YAAY;AACd;AACA,IAAM,OAAO,CAAC,IAAI,QAAQ;AAAA,EACxB,MAAM;AAAA,EACN,SAAS;AACX;AACA,SAAS,+DAA+D,IAAI,KAAK;AAC/E,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,gDAAgD,IAAI,KAAK;AAChE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,gEAAgE,GAAG,GAAG,gBAAgB,EAAE;AACzG,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,mBAAmB,OAAO,gBAAgB,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,OAAO,aAAa,OAAO,kBAAkB,CAAC,CAAC;AAAA,EAC5L;AACF;AACA,SAAS,6EAA6E,IAAI,KAAK;AAC7F,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,8DAA8D,IAAI,KAAK;AAC9E,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,8EAA8E,GAAG,GAAG,gBAAgB,EAAE;AACvH,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAU,IAAI;AACpB,UAAM,WAAW,IAAI;AACrB,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,gBAAgB,OAAO,aAAa,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,SAAS,OAAO,WAAW,QAAQ,CAAC,CAAC;AAAA,EAC5K;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,IAAI,CAAC;AACjC,IAAG,WAAW,GAAG,+DAA+D,GAAG,GAAG,gBAAgB,EAAE;AACxG,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,OAAO,YAAY;AACjC,IAAG,WAAW,OAAO,iBAAiB;AACtC,IAAG,WAAW,WAAc,gBAAgB,GAAG,KAAK,OAAO,SAAS,CAAC;AACrE,IAAG,YAAY,mBAAmB,SAAS;AAC3C,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,WAAW,OAAO,WAAW,EAAE,gBAAgB,OAAO,QAAQ;AAAA,EAC9E;AACF;AACA,SAAS,uCAAuC,IAAI,KAAK;AACvD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,OAAO,EAAE;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,WAAW,OAAO,WAAW;AAC3C,IAAG,YAAY,mBAAmB,QAAQ;AAAA,EAC5C;AACF;AACA,SAAS,oFAAoF,IAAI,KAAK;AACpG,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,qEAAqE,IAAI,KAAK;AACrF,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,qFAAqF,GAAG,GAAG,gBAAgB,EAAE;AAC9H,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAW,IAAI;AACrB,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,kBAAkB,OAAO,eAAe,EAAE,2BAA8B,gBAAgB,GAAG,MAAM,OAAO,iBAAiB,UAAU,OAAO,QAAW,gBAAgB,GAAG,MAAM,OAAO,mBAAmB,IAAI,CAAC,CAAC,CAAC;AAAA,EAC1P;AACF;AACA,SAAS,sDAAsD,IAAI,KAAK;AACtE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,sEAAsE,GAAG,GAAG,gBAAgB,EAAE;AAC/G,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,WAAW,OAAO,SAAS;AAAA,EAC3C;AACF;AACA,SAAS,mFAAmF,IAAI,KAAK;AACnG,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,oEAAoE,IAAI,KAAK;AACpF,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,oFAAoF,GAAG,GAAG,gBAAgB,EAAE;AAC7H,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,sBAAsB,OAAO,mBAAmB,EAAE,2BAA8B,gBAAgB,GAAG,MAAS,gBAAgB,GAAG,IAAI,CAAC,CAAC;AAAA,EAChL;AACF;AACA,SAAS,mEAAmE,IAAI,KAAK;AACnF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,eAAe,EAAE;AAAA,EACnC;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,cAAc,wCAAwC;AACpE,IAAG,YAAY,mBAAmB,aAAa;AAAA,EACjD;AACF;AACA,SAAS,qDAAqD,IAAI,KAAK;AACrE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,qEAAqE,GAAG,GAAG,gBAAgB,CAAC,EAAE,GAAG,oEAAoE,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAAA,EAC/O;AACA,MAAI,KAAK,GAAG;AACV,UAAM,uBAA0B,YAAY,CAAC;AAC7C,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,QAAQ,OAAO,sBAAsB,OAAO,mBAAmB,EAAE,YAAY,oBAAoB;AAAA,EACjH;AACF;AACA,SAAS,uCAAuC,IAAI,KAAK;AACvD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,WAAW,GAAG,uDAAuD,GAAG,GAAG,gBAAgB,CAAC,EAAE,GAAG,sDAAsD,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AACjN,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,YAAY,CAAC;AACzC,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,WAAc,gBAAgB,GAAG,KAAK,CAAC,OAAO,cAAc,CAAC;AAC3E,IAAG,YAAY,mBAAmB,QAAQ;AAC1C,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,kBAAkB,OAAO,eAAe,EAAE,YAAY,gBAAgB;AAAA,EACrG;AACF;AACA,SAAS,iCAAiC,IAAI,KAAK;AACjD,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,wBAAwB,CAAC;AAC5B,IAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,IAAG,WAAW,UAAU,SAAS,uDAAuD,QAAQ;AAC9F,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,kBAAkB,MAAM,CAAC;AAAA,IACxD,CAAC;AACD,IAAG,WAAW,GAAG,iDAAiD,GAAG,GAAG,gBAAgB,CAAC,EAAE,GAAG,gDAAgD,GAAG,IAAI,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,wCAAwC,GAAG,GAAG,OAAO,CAAC,EAAE,GAAG,wCAAwC,GAAG,GAAG,OAAO,CAAC;AAC5T,IAAG,aAAa;AAChB,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,oBAAuB,YAAY,CAAC;AAC1C,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,OAAO,WAAW;AAChC,IAAG,WAAW,WAAW,OAAO,MAAM,EAAE,WAAc,gBAAgB,IAAI,KAAK,OAAO,QAAQ,OAAO,MAAM,OAAO,UAAU,CAAC;AAC7H,IAAG,YAAY,MAAM,OAAO,GAAG,EAAE,YAAY,OAAO,QAAQ,EAAE,gBAAgB,UAAU,EAAE,mBAAmB,MAAM;AACnH,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,QAAQ,OAAO,mBAAmB,OAAO,gBAAgB,EAAE,YAAY,iBAAiB;AACtG,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,QAAQ,OAAO,WAAW;AACxC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,kBAAkB,OAAO,eAAe,OAAO,SAAS;AAAA,EACxF;AACF;AACA,SAAS,8DAA8D,IAAI,KAAK;AAC9E,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,+DAA+D,GAAG,GAAG,gBAAgB,EAAE;AACxG,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,mBAAmB,OAAO,gBAAgB,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,OAAO,OAAU,gBAAgB,GAAG,MAAM,OAAO,QAAQ,OAAO,aAAa,CAAC,CAAC;AAAA,EAC5N;AACF;AACA,SAAS,gCAAgC,IAAI,KAAK;AAChD,MAAI,KAAK,GAAG;AACV,IAAG,aAAa,CAAC;AACjB,IAAG,WAAW,GAAG,gDAAgD,GAAG,GAAG,gBAAgB,EAAE;AAAA,EAC3F;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,mBAAmB,OAAO,gBAAgB;AAAA,EACzE;AACF;AACA,IAAM,QAAQ,CAAC;AAAA,EACb;AACF,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAmCY,GAAG,wCAAwC,CAAC;AAAA,aACjD,GAAG,mCAAmC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iBAUnC,GAAG,kCAAkC,CAAC;AAAA,aAC1C,GAAG,kCAAkC,CAAC;AAAA,cACrC,GAAG,kCAAkC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWpD,IAAM,gBAAN,MAAM,uBAAsB,UAAU;AAAA,EACpC,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,sBAAsB,mBAAmB;AACvD,cAAQ,+BAA+B,6BAAgC,sBAAsB,cAAa,IAAI,qBAAqB,cAAa;AAAA,IAClJ;AAAA,EACF,GAAG;AAAA,EACH,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,eAAc;AAAA,EACzB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAUH,IAAI;AAAA,CACH,SAAUA,kBAAiB;AAI1B,EAAAA,iBAAgB,MAAM,IAAI;AAI1B,EAAAA,iBAAgB,SAAS,IAAI;AAI7B,EAAAA,iBAAgB,QAAQ,IAAI;AAI5B,EAAAA,iBAAgB,QAAQ,IAAI;AAI5B,EAAAA,iBAAgB,aAAa,IAAI;AACnC,GAAG,oBAAoB,kBAAkB,CAAC,EAAE;AAM5C,IAAM,WAAN,MAAM,kBAAiB,cAAc;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,KAAK;AACP,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,GAAG,KAAK;AACV,SAAK,MAAM;AAAA,EACb;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,MAAM,KAAK;AACb,SAAK,SAAS;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,aAAa;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,WAAW,KAAK;AAClB,SAAK,cAAc;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,KAAK;AAChB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,MAAM,KAAK;AACb,SAAK,SAAS;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,KAAK;AAChB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,eAAe;AACjB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,aAAa,KAAK;AACpB,SAAK,gBAAgB;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,cAAc;AAChB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,YAAY,KAAK;AACnB,SAAK,eAAe;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,cAAc;AAChB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,YAAY,KAAK;AACnB,SAAK,eAAe;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,OAAO;AACT,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,KAAK,KAAK;AACZ,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,MAAM,KAAK;AACb,SAAK,SAAS;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,cAAc;AAChB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,YAAY,KAAK;AACnB,SAAK,eAAe;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,aAAa;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,WAAW,KAAK;AAClB,SAAK,cAAc;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,OAAO,KAAK;AACd,SAAK,UAAU;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,OAAO;AACT,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,KAAK,KAAK;AACZ,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,KAAK;AAChB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,iBAAiB;AACnB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,eAAe,KAAK;AACtB,SAAK,kBAAkB;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,KAAK;AACf,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,aAAa;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,WAAW,KAAK;AAClB,SAAK,cAAc;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,aAAa;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,WAAW,KAAK;AAClB,SAAK,cAAc;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,oBAAoB;AACtB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,kBAAkB,KAAK;AACzB,SAAK,qBAAqB;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,KAAK;AACf,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,KAAK;AAChB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,KAAK;AACf,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,KAAK;AACf,SAAK,WAAW;AAChB,QAAI,OAAO,OAAO,QAAQ,UAAU;AAClC,aAAO,QAAQ,GAAG,EAAE,QAAQ,CAAC,CAAC,GAAG,CAAC,MAAM,KAAK,IAAI,CAAC,EAAE,MAAM,MAAM,KAAK,IAAI,CAAC,EAAE,IAAI,EAAE;AAClF,aAAO,QAAQ,GAAG,EAAE,QAAQ,CAAC,CAAC,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,EAAE,MAAM,MAAM,KAAK,GAAG,CAAC,EAAE,IAAI,EAAE;AAAA,IAClF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM9B,WAAW,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM5B,sBAAsB,IAAI,aAAa;AAAA,EACvC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY;AAAA,EACZ;AAAA,EACA,YAAY;AAAA,EACZ;AAAA,EACA;AAAA,EACA,eAAe;AAAA,EACf,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,eAAe;AAAA,EACf,cAAc;AAAA,EACd,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,kBAAkB;AAAA,EAClB;AAAA,EACA,cAAc;AAAA,EACd,cAAc;AAAA,EACd;AAAA,EACA;AAAA,EACA,YAAY;AAAA,EACZ;AAAA,EACA;AAAA,EACA,YAAY;AAAA,EACZ;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AAAA,EACP,iBAAiB;AAAA,EACjB,qBAAqB;AAAA,EACrB,gBAAgB;AAAA,EAChB,gBAAgB,CAAC;AAAA,EACjB,YAAY,CAAC;AAAA,EACb,cAAc,CAAC;AAAA,EACf,eAAe,CAAC;AAAA,EAChB;AAAA,EACA;AAAA,EACA,cAAc;AAAA,EACd;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,IAAI,oBAAoB;AACtB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,kBAAkB,KAAK;AACzB,SAAK,qBAAqB;AAAA,EAC5B;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK,iBAAiB;AAAA,EAC/B;AAAA,EACA,IAAI,aAAa;AACf,WAAO,KAAK,iBAAiB;AAAA,EAC/B;AAAA,EACA,IAAI,OAAO;AACT,WAAO,KAAK,iBAAiB;AAAA,EAC/B;AAAA,EACA,IAAI,cAAc;AAChB,QAAI,KAAK,UAAU,CAAC,KAAK,WAAW;AAClC,UAAI,KAAK,KAAM,QAAO,KAAK,OAAO,MAAM,KAAK,cAAc,IAAI,KAAK,MAAM,MAAM,KAAK,KAAK,IAAI,EAAE,IAAI,UAAQ,KAAK,WAAW,OAAO,KAAK,MAAM,KAAK,cAAc,IAAI,KAAK,MAAM,MAAM,KAAK,KAAK,IAAI,CAAC;AAAA,eAAW,KAAK,cAAc,KAAK,SAAU,QAAO,KAAK;AAAA,UAAY,QAAO,KAAK,OAAO,MAAM,KAAK,cAAc,IAAI,KAAK,OAAO,KAAK,IAAI;AAAA,IACjV;AACA,WAAO,CAAC;AAAA,EACV;AAAA,EACA,IAAI,aAAa;AACf,WAAO,KAAK,YAAY,KAAK,kBAAkB,KAAK,YAAY,CAAC,IAAI,KAAK;AAAA,EAC5E;AAAA,EACA,IAAI,gBAAgB;AAClB,QAAI,KAAK,aAAa,KAAK,QAAQ,KAAK,aAAa;AACnD,aAAO,KAAK,aAAa,KAAK,kBAAkB,KAAK,OAAO,KAAK,UAAU,CAAC,IAAI,KAAK,YAAY,KAAK,SAAS,MAAM,KAAK,OAAO,KAAK,MAAM,OAAO,KAAK,OAAO,KAAK,OAAO,KAAK,KAAK,OAAO,KAAK,IAAI;AAAA,IACvM;AACA,WAAO,KAAK;AAAA,EACd;AAAA,EACA,kBAAkB,OAAO,aAAa;AAAA,EACtC,YAAY,MAAM;AAChB,UAAM;AACN,SAAK,OAAO;AAAA,EACd;AAAA,EACA,WAAW;AACT,UAAM,SAAS;AACf,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,YAAY,eAAe;AACzB,UAAM,YAAY,aAAa;AAC/B,QAAI,mBAAmB;AACvB,QAAI,KAAK,gBAAgB,QAAQ;AAC/B,WAAK,SAAS;AAAA,IAChB;AACA,QAAI,cAAc,SAAS;AACzB,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,cAAc;AAClB,UAAI,KAAK,QAAQ,kBAAkB,gBAAgB,iBAAiB,KAAK,WAAW;AAClF,aAAK,YAAY;AACjB,2BAAmB;AAAA,MACrB;AAAA,IACF;AACA,QAAI,cAAc,aAAa;AAC7B,WAAK,gBAAgB,KAAK,OAAO;AAAA,QAC/B,KAAK;AAAA,QACL,MAAM;AAAA,MACR,IAAI;AAAA,IACN;AACA,QAAI,cAAc,mBAAmB;AACnC,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,cAAc;AAClB,UAAI,kBAAkB,gBAAgB,iBAAiB,KAAK,qBAAqB;AAC/E,aAAK,sBAAsB;AAAA,MAC7B;AAAA,IACF;AACA,QAAI,cAAc,SAAS;AACzB,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,cAAc;AAClB,UAAI,KAAK,QAAQ,eAAe,YAAY,cAAc,WAAW,cAAc,YAAY,KAAK,WAAW;AAC7G,aAAK,YAAY,aAAa;AAC9B,2BAAmB;AAAA,MACrB;AACA,UAAI,eAAe,sBAAsB,cAAc,qBAAqB,cAAc,sBAAsB,KAAK,qBAAqB;AACxI,aAAK,sBAAsB,aAAa;AAAA,MAC1C;AAAA,IACF;AACA,QAAI,KAAK,aAAa;AACpB,YAAM,YAAY,CAAC,qBAAqB,cAAc,OAAO,eAAe,WAAW,cAAc,OAAO,cAAc,UAAU,cAAc,YAAY,cAAc,gBAAgB,cAAc;AAC1M,UAAI,WAAW;AACb,aAAK,KAAK;AACV,aAAK,kBAAkB;AAAA,MACzB;AAAA,IACF;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,SAAK,UAAU,QAAQ,UAAQ;AAC7B,cAAQ,KAAK,QAAQ,GAAG;AAAA,QACtB,KAAK;AACH,eAAK,mBAAmB,KAAK;AAC7B;AAAA,QACF,KAAK;AACH,eAAK,gBAAgB,KAAK;AAC1B;AAAA,QACF,KAAK;AACH,eAAK,kBAAkB,KAAK;AAC5B;AAAA,QACF,KAAK;AACH,eAAK,sBAAsB,KAAK;AAChC;AAAA,QACF;AACE,eAAK,gBAAgB,KAAK;AAC1B;AAAA,MACJ;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,kBAAkB;AAChB,UAAM,gBAAgB;AACtB,YAAQ,QAAQ,EAAE,KAAK,MAAM;AAC3B,WAAK,SAAS;AAAA,IAChB,CAAC;AAAA,EACH;AAAA,EACA,qBAAqB;AACnB,QAAI,CAAC,KAAK,aAAa;AACrB,WAAK,SAAS;AAAA,IAChB;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,qBAAqB;AAC1B,SAAK,YAAY;AACjB,SAAK,cAAc;AACnB,UAAM,YAAY;AAAA,EACpB;AAAA,EACA,WAAW;AACT,QAAI,kBAAkB,KAAK,UAAU,KAAK,CAAC,KAAK,aAAa;AAC3D,UAAI,UAAU,KAAK,kBAAkB,aAAa,GAAG;AACnD,aAAK,gBAAgB;AACrB,aAAK,aAAa,KAAK,SAAS;AAChC,aAAK,KAAK;AACV,aAAK,eAAe,SAAS,KAAK,kBAAkB,aAAa;AACjE,aAAK,gBAAgB,UAAU,KAAK,kBAAkB,aAAa;AACnE,aAAK,sBAAsB,SAAS,KAAK,SAAS;AAClD,aAAK,uBAAuB,UAAU,KAAK,SAAS;AACpD,aAAK,cAAc;AAAA,MACrB;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO;AACL,QAAI,CAAC,KAAK,WAAW;AACnB,WAAK,QAAQ;AACb,WAAK,iBAAiB;AACtB,WAAK,cAAc;AACnB,WAAK,mBAAmB;AACxB,WAAK,GAAG,cAAc;AAAA,IACxB;AAAA,EACF;AAAA,EACA,aAAa,IAAI;AACf,SAAK,YAAY,MAAM,KAAK,kBAAkB,iBAAiB,WAAW,KAAK,kBAAkB,eAAe,4BAA4B;AAAA,EAC9I;AAAA,EACA,kBAAkB;AAChB,SAAK,QAAQ,KAAK,OAAO;AAAA,MACvB,MAAM;AAAA,MACN,MAAM;AAAA,IACR,IAAI;AACJ,SAAK,OAAO,KAAK,OAAO;AAAA,MACtB,MAAM;AAAA,MACN,MAAM;AAAA,IACR,IAAI;AACJ,SAAK,qBAAqB,KAAK,OAAO;AAAA,MACpC,MAAM;AAAA,MACN,MAAM;AAAA,IACR,IAAI;AACJ,SAAK,gBAAgB,KAAK,OAAO;AAAA,MAC/B,KAAK;AAAA,MACL,MAAM;AAAA,IACR,IAAI;AACJ,SAAK,YAAY,KAAK,YAAY;AAClC,SAAK,sBAAsB,KAAK;AAChC,SAAK,YAAY,CAAC;AAAA,EACpB;AAAA,EACA,gBAAgB;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,eAAe,OAAO;AACpB,WAAO,KAAK,QAAQ,SAAS,KAAK,SAAS,KAAK,sBAAsB,MAAM,KAAK,SAAS,EAAE;AAAA,EAC9F;AAAA,EACA,cAAc,OAAO;AACnB,WAAO,KAAK,QAAQ,KAAK,SAAS,KAAK,eAAe,SAAS,KAAK,KAAK,IAAI;AAAA,EAC/E;AAAA,EACA,SAAS,SAAS;AAEhB,SAAK,kBAAkB,eAAe,SAAS,OAAO;AAAA,EACxD;AAAA,EACA,cAAc,OAAO,WAAW,QAAQ;AACtC,UAAM,QAAQ,KAAK,OAAO,MAAM,MAAM,OAAK,IAAI,EAAE,IAAI,QAAQ;AAC7D,QAAI,OAAO;AACT,YAAM,QAAQ,KAAK;AACnB,YAAM;AAAA,QACJ,YAAY;AAAA,QACZ,aAAa;AAAA,MACf,IAAI,KAAK,kBAAkB;AAC3B,YAAM;AAAA,QACJ;AAAA,MACF,IAAI,KAAK,kBAAkB;AAC3B,YAAM,aAAa,KAAK,mBAAmB;AAC3C,YAAM,WAAW,KAAK;AACtB,YAAM,iBAAiB,CAAC,SAAS,GAAG,UAAU,UAAU,QAAQ,IAAI;AACpE,YAAM,iBAAiB,CAAC,QAAQ,OAAO,UAAU,SAAS,QAAQ;AAClE,YAAM,WAAW,CAAC,OAAO,GAAG,MAAM,MAAM,KAAK,SAAS;AAAA,QACpD;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AACD,UAAI,WAAW,KAAK,OAAO;AAAA,QACzB,MAAM;AAAA,QACN,MAAM;AAAA,MACR,IAAI;AACJ,UAAI,iBAAiB,OACnB,kBAAkB;AACpB,UAAI,KAAK,MAAM;AACb,mBAAW;AAAA,UACT,MAAM,eAAe,MAAM,CAAC,GAAG,kBAAkB,CAAC,CAAC;AAAA,UACnD,MAAM,eAAe,MAAM,CAAC,GAAG,kBAAkB,CAAC,CAAC;AAAA,QACrD;AACA,iBAAS,eAAe,SAAS,MAAM,SAAS,CAAC,GAAG,WAAW,IAAI,GAAG,eAAe,SAAS,MAAM,SAAS,CAAC,GAAG,WAAW,GAAG,CAAC;AAChI,0BAAkB,KAAK,cAAc,QAAQ,aAAa,KAAK,cAAc,SAAS;AACtF,yBAAiB,SAAS,SAAS,MAAM,QAAQ,SAAS,SAAS,MAAM;AAAA,MAC3E,OAAO;AACL,mBAAW,eAAe,OAAO,iBAAiB;AAClD,aAAK,aAAa,SAAS,eAAe,UAAU,UAAU,WAAW,IAAI,GAAG,SAAS,IAAI,SAAS,YAAY,eAAe,UAAU,UAAU,WAAW,GAAG,CAAC;AACpK,0BAAkB,KAAK,mBAAmB,KAAK,aAAa,aAAa;AACzE,yBAAiB,aAAa;AAAA,MAChC;AACA,WAAK,iBAAiB;AACtB,0BAAoB,KAAK,QAAQ;AAAA,IACnC;AAAA,EACF;AAAA,EACA,aAAa,OAAO,IAAI,WAAW,QAAQ;AACzC,QAAI,IAAI;AACN,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,KAAK,iBAAiB;AAC1B,YAAM,WAAW,CAAC,OAAO,GAAG,MAAM,MAAM,KAAK,SAAS;AAAA,QACpD;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AACD,YAAM,YAAY,OAAO;AACzB,YAAM,UAAU,OAAO;AACvB,UAAI,WAAW;AACb,YAAI,KAAK,MAAM;AACb,cAAI,SAAS,MAAM,OAAO,MAAM,OAAO,MAAM,CAAC,GAAG;AAC/C,qBAAS,SAAS,MAAM,OAAO,KAAK,UAAU,CAAC,IAAI,SAAS,MAAM,OAAO,KAAK,KAAK,UAAU,CAAC,CAAC;AAAA,UACjG,WAAW,SAAS,MAAM,OAAO,MAAM,OAAO,MAAM,CAAC,GAAG;AACtD,sBAAU,SAAS,MAAM,OAAO,KAAK,KAAK,UAAU,CAAC,GAAG,SAAS,MAAM,OAAO,KAAK,UAAU,CAAC,CAAC;AAAA,UACjG;AAAA,QACF,OAAO;AACL,cAAI,SAAS,QAAQ,QAAQ,OAAO;AAClC,kBAAM,OAAO,SAAS,QAAQ,KAAK,KAAK;AACxC,iBAAK,aAAa,SAAS,KAAK,CAAC,IAAI,SAAS,GAAG,GAAG;AAAA,UACtD;AAAA,QACF;AAAA,MACF,WAAW,SAAS;AAClB,YAAI,KAAK,MAAM;AACb,cAAI,SAAS,KAAK,OAAO,MAAM,QAAQ,MAAM,CAAC,IAAI,GAAG;AACnD,qBAAS,SAAS,MAAM,OAAO,KAAK,UAAU,CAAC,IAAI,SAAS,MAAM,OAAO,KAAK,KAAK,UAAU,CAAC,CAAC;AAAA,UACjG,WAAW,SAAS,KAAK,OAAO,MAAM,QAAQ,MAAM,CAAC,IAAI,GAAG;AAC1D,sBAAU,SAAS,MAAM,OAAO,KAAK,KAAK,UAAU,CAAC,GAAG,SAAS,MAAM,OAAO,KAAK,UAAU,CAAC,CAAC;AAAA,UACjG;AAAA,QACF,OAAO;AACL,cAAI,SAAS,OAAO,SAAS,QAAQ,GAAG;AACtC,kBAAM,OAAO,SAAS,QAAQ,KAAK,KAAK;AACxC,iBAAK,aAAa,SAAS,KAAK,CAAC,IAAI,SAAS,GAAG,GAAG;AAAA,UACtD;AAAA,QACF;AAAA,MACF;AAAA,IACF,OAAO;AACL,WAAK,cAAc,OAAO,QAAQ;AAAA,IACpC;AAAA,EACF;AAAA,EACA,mBAAmB;AACjB,UAAM,2BAA2B,CAAC,MAAM,UAAU,SAAS,OAAO,KAAK,MAAM,QAAQ,SAAS,KAAK,IAAI;AACvG,QAAI,kBAAkB,KAAK;AAC3B,QAAI,iBAAiB;AACrB,QAAI,KAAK,kBAAkB,eAAe;AACxC,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,KAAK,iBAAiB;AAC1B,UAAI,KAAK,MAAM;AACb,0BAAkB;AAAA,UAChB,MAAM,yBAAyB,WAAW,KAAK,UAAU,CAAC,CAAC;AAAA,UAC3D,MAAM,yBAAyB,YAAY,KAAK,UAAU,CAAC,CAAC;AAAA,QAC9D;AACA,yBAAiB;AAAA,UACf,MAAM,gBAAgB,OAAO,KAAK,mBAAmB;AAAA,UACrD,MAAM,gBAAgB,OAAO,KAAK,mBAAmB;AAAA,QACvD;AAAA,MACF,OAAO;AACL,cAAM,YAAY,KAAK,aAAa,aAAa;AACjD,0BAAkB,yBAAyB,WAAW,KAAK,SAAS;AACpE,yBAAiB,kBAAkB,KAAK;AAAA,MAC1C;AAAA,IACF;AACA,WAAO;AAAA,MACL,OAAO,KAAK;AAAA,MACZ,MAAM,KAAK;AAAA,MACX,UAAU;AAAA,QACR,OAAO;AAAA,QACP,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAAA,EACA,oBAAoB;AAClB,UAAM,aAAa,KAAK,mBAAmB;AAC3C,UAAM,gBAAgB,KAAK,kBAAkB,gBAAgB,KAAK,iBAAiB,cAAc,cAAc,WAAW,OAAO,MAAM;AACvI,UAAM,iBAAiB,KAAK,kBAAkB,gBAAgB,KAAK,iBAAiB,cAAc,eAAe,WAAW,MAAM,MAAM;AACxI,UAAM,8BAA8B,CAAC,cAAc,cAAc,aAAa,eAAe,KAAK,KAAK,gBAAgB,aAAa,aAAa,IAAI;AACrJ,UAAM,6BAA6B,eAAa,KAAK,KAAK,YAAY,CAAC;AACvE,UAAM,qBAAqB,KAAK,OAAO;AAAA,MACrC,MAAM,4BAA4B,eAAe,KAAK,UAAU,CAAC,CAAC;AAAA,MAClE,MAAM,4BAA4B,cAAc,KAAK,UAAU,CAAC,CAAC;AAAA,IACnE,IAAI,4BAA4B,KAAK,aAAa,eAAe,eAAe,KAAK,SAAS;AAC9F,UAAM,oBAAoB,KAAK,wBAAwB,KAAK,OAAO,CAAC,2BAA2B,mBAAmB,IAAI,GAAG,2BAA2B,mBAAmB,IAAI,CAAC,IAAI,2BAA2B,kBAAkB;AAC7N,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,mBAAmB;AACjB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,KAAK,kBAAkB;AAC3B,UAAM,gBAAgB,CAAC,QAAQ,MAAM,OAAO,UAAU,UAAU,KAAK,QAAQ,SAAS,QAAQ,SAAS,QAAQ,IAAI,KAAK,OAAO,OAAO;AACtI,UAAM,QAAQ,KAAK;AACnB,UAAM,OAAO,KAAK,OAAO;AAAA,MACvB,MAAM,cAAc,KAAK,MAAM,MAAM,mBAAmB,MAAM,kBAAkB,CAAC,CAAC;AAAA,MAClF,MAAM,cAAc,KAAK,MAAM,MAAM,mBAAmB,MAAM,kBAAkB,CAAC,GAAG,IAAI;AAAA,IAC1F,IAAI,cAAc,KAAK,OAAO,oBAAoB,iBAAiB;AACnE,SAAK,OAAO;AACZ,SAAK,qBAAqB;AAC1B,SAAK,sBAAsB;AAC3B,QAAI,KAAK,YAAY;AACnB,WAAK,YAAY,KAAK,OAAO,MAAM,KAAK;AAAA,QACtC,QAAQ,mBAAmB;AAAA,MAC7B,CAAC,EAAE,IAAI,MAAM,MAAM,KAAK;AAAA,QACtB,QAAQ,mBAAmB;AAAA,MAC7B,CAAC,CAAC,IAAI,MAAM,KAAK;AAAA,QACf,QAAQ;AAAA,MACV,CAAC;AAAA,IACH;AACA,QAAI,KAAK,OAAO;AACd,cAAQ,QAAQ,EAAE,KAAK,MAAM;AAC3B,aAAK,gBAAgB;AAAA,UACnB,OAAO,KAAK,QAAQ,KAAK,OAAO;AAAA,YAC9B,MAAM;AAAA,YACN,MAAM,MAAM;AAAA,UACd,IAAI,IAAI;AAAA,UACR,MAAM,KAAK,IAAI,KAAK,QAAQ,KAAK,QAAQ,KAAK,MAAM,KAAK,MAAM,MAAM;AAAA,QACvE;AACA,aAAK,aAAa,cAAc,KAAK,aAAa;AAAA,MACpD,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,oBAAoB;AAClB,QAAI,KAAK,aAAa,CAAC,KAAK,WAAW;AACrC,cAAQ,QAAQ,EAAE,KAAK,MAAM;AAC3B,YAAI,KAAK,WAAW;AAClB,eAAK,UAAU,MAAM,YAAY,KAAK,UAAU,MAAM,WAAW;AACjE,eAAK,UAAU,MAAM,WAAW;AAChC,eAAK,iBAAiB,cAAc,MAAM,UAAU;AACpD,gBAAM,CAAC,cAAc,aAAa,IAAI,CAAC,SAAS,KAAK,SAAS,GAAG,UAAU,KAAK,SAAS,CAAC;AAC1F,2BAAiB,KAAK,wBAAwB,KAAK,iBAAiB,cAAc,MAAM,QAAQ;AAChG,4BAAkB,KAAK,yBAAyB,KAAK,iBAAiB,cAAc,MAAM,SAAS;AACnG,gBAAM,CAAC,OAAO,MAAM,IAAI,CAAC,SAAS,KAAK,iBAAiB,aAAa,GAAG,UAAU,KAAK,iBAAiB,aAAa,CAAC;AACtH,WAAC,KAAK,QAAQ,KAAK,gBAAgB,KAAK,iBAAiB,cAAc,MAAM,QAAQ,QAAQ,KAAK,eAAe,QAAQ,OAAO,KAAK,gBAAgB,KAAK,eAAe;AACzK,WAAC,KAAK,QAAQ,KAAK,cAAc,KAAK,iBAAiB,cAAc,MAAM,SAAS,SAAS,KAAK,gBAAgB,SAAS,OAAO,KAAK,iBAAiB,KAAK,gBAAgB;AAC7K,eAAK,UAAU,MAAM,YAAY,KAAK,UAAU,MAAM,WAAW;AACjE,eAAK,UAAU,MAAM,WAAW;AAChC,eAAK,iBAAiB,cAAc,MAAM,UAAU;AAAA,QACtD;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,QAAQ,OAAO,GAAG,SAAS,OAAO;AAChC,WAAO,KAAK,SAAS,KAAK,IAAI,UAAU,KAAK,YAAY,KAAK,OAAO,CAAC,GAAG,SAAS,KAAK,OAAO,QAAQ,IAAI,IAAI;AAAA,EAChH;AAAA,EACA,qBAAqB;AACnB,QAAI,KAAK,WAAW;AAClB,YAAM,QAAQ,iBAAiB,KAAK,SAAS;AAC7C,YAAM,OAAO,WAAW,MAAM,WAAW,IAAI,KAAK,IAAI,WAAW,MAAM,IAAI,KAAK,GAAG,CAAC;AACpF,YAAM,QAAQ,WAAW,MAAM,YAAY,IAAI,KAAK,IAAI,WAAW,MAAM,KAAK,KAAK,GAAG,CAAC;AACvF,YAAM,MAAM,WAAW,MAAM,UAAU,IAAI,KAAK,IAAI,WAAW,MAAM,GAAG,KAAK,GAAG,CAAC;AACjF,YAAM,SAAS,WAAW,MAAM,aAAa,IAAI,KAAK,IAAI,WAAW,MAAM,MAAM,KAAK,GAAG,CAAC;AAC1F,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,GAAG,OAAO;AAAA,QACV,GAAG,MAAM;AAAA,MACX;AAAA,IACF;AACA,WAAO;AAAA,MACL,MAAM;AAAA,MACN,OAAO;AAAA,MACP,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAAA,EACF;AAAA,EACA,UAAU;AACR,QAAI,KAAK,kBAAkB,eAAe;AACxC,YAAM,gBAAgB,KAAK,iBAAiB,cAAc,cAAc;AACxE,YAAM,QAAQ,KAAK,gBAAgB,GAAG,KAAK,iBAAiB,cAAc,eAAe,cAAc,WAAW;AAClH,YAAM,SAAS,KAAK,iBAAiB,GAAG,KAAK,iBAAiB,cAAc,gBAAgB,cAAc,YAAY;AACtH,YAAM,UAAU,CAAC,OAAO,WAAW,KAAK,iBAAiB,cAAc,MAAM,KAAK,IAAI;AACtF,UAAI,KAAK,QAAQ,KAAK,YAAY;AAChC,gBAAQ,UAAU,MAAM;AACxB,gBAAQ,SAAS,KAAK;AAAA,MACxB,OAAO;AACL,gBAAQ,UAAU,MAAM;AAAA,MAC1B;AAAA,IACF;AAAA,EACF;AAAA,EACA,gBAAgB;AACd,QAAI,KAAK,QAAQ;AACf,YAAM,aAAa,KAAK,mBAAmB;AAC3C,YAAM,UAAU,CAAC,OAAO,QAAQ,OAAO,QAAQ,MAAM,KAAK,cAAc,kCACnE,KAAK,cACL;AAAA,QACD,CAAC,GAAG,KAAK,EAAE,IAAI,UAAU,CAAC,GAAG,SAAS,QAAQ,QAAQ;AAAA,MACxD;AAEF,UAAI,KAAK,MAAM;AACb,gBAAQ,UAAU,KAAK,QAAQ,KAAK,UAAU,CAAC,GAAG,WAAW,CAAC;AAC9D,gBAAQ,SAAS,KAAK,YAAY,KAAK,OAAO,CAAC,GAAG,KAAK,UAAU,CAAC,GAAG,WAAW,CAAC;AAAA,MACnF,OAAO;AACL,aAAK,aAAa,QAAQ,SAAS,KAAK,YAAY,KAAK,QAAQ,KAAK,WAAW,WAAW,CAAC,IAAI,QAAQ,UAAU,KAAK,QAAQ,KAAK,WAAW,WAAW,CAAC;AAAA,MAC9J;AAAA,IACF;AAAA,EACF;AAAA,EACA,mBAAmB,KAAK;AACtB,QAAI,KAAK,aAAa,CAAC,KAAK,aAAa;AACvC,YAAM,QAAQ,MAAM,IAAI,QAAQ,KAAK;AACrC,YAAM,wBAAwB,CAAC,QAAQ,UAAU,SAAS;AAC1D,YAAM,eAAe,CAAC,KAAK,GAAG,KAAK,MAAM,KAAK,eAAe,kCACxD,KAAK,eACL;AAAA,QACD,WAAW,eAAe,EAAE,OAAO,EAAE;AAAA,MACvC;AAEF,UAAI,KAAK,MAAM;AACb,qBAAa,sBAAsB,MAAM,MAAM,KAAK,UAAU,CAAC,CAAC,GAAG,sBAAsB,MAAM,MAAM,KAAK,UAAU,CAAC,CAAC,CAAC;AAAA,MACzH,OAAO;AACL,cAAM,eAAe,sBAAsB,OAAO,KAAK,SAAS;AAChE,aAAK,aAAa,aAAa,cAAc,CAAC,IAAI,aAAa,GAAG,YAAY;AAAA,MAChF;AAAA,IACF;AAAA,EACF;AAAA,EACA,uBAAuB,OAAO;AAC5B,UAAM,SAAS,MAAM;AACrB,UAAM,aAAa,KAAK,mBAAmB;AAC3C,UAAM,qBAAqB,CAAC,MAAM,UAAU,OAAO,OAAO,QAAQ,OAAO,QAAQ,OAAO;AACxF,UAAM,wBAAwB,CAAC,MAAM,UAAU,SAAS,OAAO,KAAK,MAAM,QAAQ,SAAS,KAAK,IAAI;AACpG,UAAM,wBAAwB,CAAC,eAAe,QAAQ,OAAO,MAAM,OAAO,yBAAyB;AACjG,aAAO,iBAAiB,QAAQ,QAAQ,uBAAuB,QAAQ,OAAO,QAAQ,SAAS,QAAQ;AAAA,IACzG;AACA,UAAM,iBAAiB,CAAC,eAAe,eAAe,QAAQ,OAAO,MAAM,OAAO,yBAAyB;AACzG,UAAI,iBAAiB,MAAO,QAAO;AAAA,UAAO,QAAO,KAAK,IAAI,GAAG,uBAAuB,gBAAgB,gBAAgB,SAAS,gBAAgB,QAAQ,gBAAgB,gBAAgB,SAAS,gBAAgB,IAAI,KAAK;AAAA,IACzN;AACA,UAAM,gBAAgB,CAAC,eAAe,QAAQ,OAAO,MAAM,OAAO,UAAU,UAAU;AACpF,UAAI,YAAY,SAAS,OAAO,IAAI;AACpC,UAAI,iBAAiB,OAAO;AAC1B,qBAAa,QAAQ;AAAA,MACvB;AACA,aAAO,KAAK,QAAQ,WAAW,OAAO;AAAA,IACxC;AACA,UAAM,YAAY,mBAAmB,OAAO,WAAW,WAAW,GAAG;AACrE,UAAM,aAAa,mBAAmB,OAAO,YAAY,WAAW,IAAI;AACxE,QAAI,WAAW,KAAK,OAAO;AAAA,MACzB,MAAM;AAAA,MACN,MAAM;AAAA,IACR,IAAI;AACJ,QAAI,UAAU,KAAK;AACnB,QAAI,iBAAiB;AACrB,QAAI,eAAe,KAAK;AACxB,QAAI,KAAK,MAAM;AACb,YAAM,eAAe,KAAK,cAAc,OAAO;AAC/C,YAAM,gBAAgB,KAAK,cAAc,QAAQ;AACjD,UAAI,CAAC,KAAK,eAAe,KAAK,gBAAgB,gBAAgB,gBAAgB;AAC5E,cAAM,eAAe;AAAA,UACnB,MAAM,sBAAsB,WAAW,KAAK,UAAU,CAAC,CAAC;AAAA,UACxD,MAAM,sBAAsB,YAAY,KAAK,UAAU,CAAC,CAAC;AAAA,QAC3D;AACA,cAAM,eAAe;AAAA,UACnB,MAAM,sBAAsB,aAAa,MAAM,KAAK,MAAM,MAAM,KAAK,KAAK,MAAM,KAAK,mBAAmB,MAAM,KAAK,oBAAoB,CAAC,GAAG,YAAY;AAAA,UACvJ,MAAM,sBAAsB,aAAa,MAAM,KAAK,MAAM,MAAM,KAAK,KAAK,MAAM,KAAK,mBAAmB,MAAM,KAAK,oBAAoB,CAAC,GAAG,aAAa;AAAA,QAC1J;AACA,mBAAW;AAAA,UACT,MAAM,eAAe,aAAa,MAAM,aAAa,MAAM,KAAK,MAAM,MAAM,KAAK,KAAK,MAAM,KAAK,mBAAmB,MAAM,KAAK,oBAAoB,CAAC,GAAG,YAAY;AAAA,UACnK,MAAM,eAAe,aAAa,MAAM,aAAa,MAAM,KAAK,MAAM,MAAM,KAAK,KAAK,MAAM,KAAK,mBAAmB,MAAM,KAAK,oBAAoB,CAAC,GAAG,aAAa;AAAA,QACtK;AACA,kBAAU;AAAA,UACR,MAAM,cAAc,aAAa,MAAM,SAAS,MAAM,KAAK,KAAK,MAAM,KAAK,mBAAmB,MAAM,KAAK,oBAAoB,CAAC,CAAC;AAAA,UAC/H,MAAM,cAAc,aAAa,MAAM,SAAS,MAAM,KAAK,KAAK,MAAM,KAAK,mBAAmB,MAAM,KAAK,oBAAoB,CAAC,GAAG,IAAI;AAAA,QACvI;AACA,yBAAiB,SAAS,SAAS,KAAK,MAAM,QAAQ,QAAQ,SAAS,KAAK,KAAK,QAAQ,SAAS,SAAS,KAAK,MAAM,QAAQ,QAAQ,SAAS,KAAK,KAAK,QAAQ,KAAK;AACtK,uBAAe;AAAA,UACb,KAAK;AAAA,UACL,MAAM;AAAA,QACR;AAAA,MACF;AAAA,IACF,OAAO;AACL,YAAM,YAAY,KAAK,aAAa,aAAa;AACjD,YAAM,sBAAsB,KAAK,iBAAiB;AAClD,UAAI,CAAC,KAAK,eAAe,KAAK,eAAe,qBAAqB;AAChE,cAAM,eAAe,sBAAsB,WAAW,KAAK,SAAS;AACpE,cAAM,eAAe,sBAAsB,cAAc,KAAK,OAAO,KAAK,MAAM,KAAK,oBAAoB,KAAK,qBAAqB,mBAAmB;AACtJ,mBAAW,eAAe,cAAc,cAAc,KAAK,OAAO,KAAK,MAAM,KAAK,oBAAoB,KAAK,qBAAqB,mBAAmB;AACnJ,kBAAU,cAAc,cAAc,UAAU,KAAK,MAAM,KAAK,oBAAoB,KAAK,mBAAmB;AAC5G,yBAAiB,aAAa,KAAK,SAAS,YAAY,KAAK,QAAQ,KAAK;AAC1E,uBAAe;AAAA,MACjB;AAAA,IACF;AACA,WAAO;AAAA,MACL,OAAO;AAAA,MACP,MAAM;AAAA,MACN;AAAA,MACA,WAAW;AAAA,IACb;AAAA,EACF;AAAA,EACA,eAAe,OAAO;AACpB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,KAAK,uBAAuB,KAAK;AACrC,QAAI,gBAAgB;AAClB,YAAM,WAAW;AAAA,QACf;AAAA,QACA;AAAA,MACF;AACA,WAAK,mBAAmB,QAAQ;AAChC,WAAK,QAAQ;AACb,WAAK,OAAO;AACZ,WAAK,gBAAgB;AACrB,WAAK,aAAa,uBAAuB,QAAQ;AACjD,UAAI,KAAK,SAAS,KAAK,cAAc,KAAK,GAAG;AAC3C,cAAM,gBAAgB;AAAA,UACpB,OAAO,KAAK,QAAQ,KAAK,IAAI,KAAK,eAAe,KAAK,IAAI,KAAK,OAAO,KAAK,MAAM,SAAS,KAAK,KAAK,IAAI;AAAA,UACxG,MAAM,KAAK,IAAI,KAAK,SAAS,KAAK,eAAe,KAAK,IAAI,KAAK,KAAK,QAAQ,MAAM,KAAK,MAAM,MAAM;AAAA,QACrG;AACA,cAAM,qBAAqB,KAAK,cAAc,UAAU,cAAc,SAAS,KAAK,cAAc,SAAS,cAAc;AACzH,8BAAsB,KAAK,aAAa,cAAc,aAAa;AACnE,aAAK,gBAAgB;AAAA,MACvB;AAAA,IACF;AAAA,EACF;AAAA,EACA,kBAAkB,OAAO;AACvB,SAAK,aAAa,YAAY;AAAA,MAC5B,eAAe;AAAA,IACjB,CAAC;AACD,QAAI,KAAK,UAAU,KAAK,cAAc,GAAG;AACvC,UAAI,KAAK,eAAe;AACtB,qBAAa,KAAK,aAAa;AAAA,MACjC;AACA,UAAI,CAAC,KAAK,aAAa,KAAK,YAAY;AACtC,cAAM;AAAA,UACJ;AAAA,QACF,IAAI,KAAK,uBAAuB,KAAK;AACrC,cAAM,UAAU,mBAAmB,KAAK,QAAQ,KAAK,cAAc,IAAI;AACvE,YAAI,SAAS;AACX,eAAK,YAAY;AACjB,eAAK,GAAG,cAAc;AAAA,QACxB;AAAA,MACF;AACA,WAAK,gBAAgB,WAAW,MAAM;AACpC,aAAK,eAAe,KAAK;AACzB,YAAI,KAAK,aAAa,KAAK,eAAe,CAAC,KAAK,SAAS,KAAK,aAAa,SAAY;AACrF,eAAK,YAAY;AACjB,eAAK,OAAO,KAAK,eAAe;AAAA,QAClC;AACA,aAAK,GAAG,cAAc;AAAA,MACxB,GAAG,KAAK,MAAM;AAAA,IAChB,OAAO;AACL,OAAC,KAAK,aAAa,KAAK,eAAe,KAAK;AAAA,IAC9C;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,QAAI,kBAAkB,KAAK,UAAU,GAAG;AACtC,UAAI,CAAC,KAAK,sBAAsB;AAC9B,aAAK,KAAK,kBAAkB,MAAM;AAChC,gBAAM,SAAS,KAAK,SAAS;AAC7B,gBAAM,QAAQ,cAAc,IAAI,sBAAsB;AACtD,eAAK,uBAAuB,KAAK,SAAS,OAAO,QAAQ,OAAO,KAAK,eAAe,KAAK,IAAI,CAAC;AAAA,QAChG,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAAA,EACA,uBAAuB;AACrB,QAAI,KAAK,sBAAsB;AAC7B,WAAK,qBAAqB;AAC1B,WAAK,uBAAuB;AAAA,IAC9B;AAAA,EACF;AAAA,EACA,iBAAiB;AACf,QAAI,KAAK,eAAe;AACtB,mBAAa,KAAK,aAAa;AAAA,IACjC;AACA,SAAK,gBAAgB,WAAW,MAAM;AACpC,UAAI,UAAU,KAAK,kBAAkB,aAAa,GAAG;AACnD,cAAM,CAAC,OAAO,MAAM,IAAI,CAAC,SAAS,KAAK,kBAAkB,aAAa,GAAG,UAAU,KAAK,kBAAkB,aAAa,CAAC;AACxH,cAAM,CAAC,aAAa,YAAY,IAAI,CAAC,UAAU,KAAK,cAAc,WAAW,KAAK,aAAa;AAC/F,cAAM,SAAS,KAAK,OAAO,eAAe,eAAe,KAAK,aAAa,cAAc,KAAK,WAAW,eAAe;AACxH,kBAAU,KAAK,KAAK,IAAI,MAAM;AAC5B,eAAK,sBAAsB,KAAK;AAChC,eAAK,eAAe;AACpB,eAAK,gBAAgB;AACrB,eAAK,sBAAsB,SAAS,KAAK,SAAS;AAClD,eAAK,uBAAuB,UAAU,KAAK,SAAS;AACpD,eAAK,KAAK;AAAA,QACZ,CAAC;AAAA,MACH;AAAA,IACF,GAAG,KAAK,YAAY;AAAA,EACtB;AAAA,EACA,aAAa,MAAM,QAAQ;AAEzB,WAAO,KAAK,WAAW,KAAK,QAAQ,IAAI,IAAI,KAAK,QAAQ,IAAI,EAAE,MAAM,IAAI,KAAK,IAAI,EAAE,KAAK,MAAM;AAAA,EACjG;AAAA,EACA,oBAAoB;AAClB,WAAO;AAAA,MACL,mBAAmB,6BAA6B,KAAK,YAAY,8BAA8B,EAAE;AAAA,MACjG,OAAO,KAAK;AAAA,MACZ,gBAAgB,WAAS,KAAK,WAAW,KAAK;AAAA,MAC9C,SAAS,KAAK;AAAA,MACd,kBAAkB,CAAC,OAAO,YAAY,KAAK,iBAAiB,OAAO,OAAO;AAAA,MAC1E,UAAU,KAAK;AAAA,MACf,MAAM,KAAK;AAAA,MACX,SAAS,KAAK;AAAA,MACd,aAAa,KAAK;AAAA,MAClB,cAAc,KAAK;AAAA,MACnB,UAAU,KAAK;AAAA,MACf,YAAY,KAAK;AAAA,MACjB,MAAM,KAAK;AAAA,IACb;AAAA,EACF;AAAA,EACA,WAAW,eAAe;AACxB,UAAM,SAAS,KAAK,UAAU,CAAC,GAAG;AAClC,UAAM,QAAQ,KAAK,OAAO,KAAK,MAAM,OAAO,gBAAgB,KAAK,QAAQ;AACzE,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA,OAAO,UAAU;AAAA,MACjB,MAAM,UAAU,QAAQ;AAAA,MACxB,MAAM,QAAQ,MAAM;AAAA,MACpB,KAAK,QAAQ,MAAM;AAAA,IACrB;AAAA,EACF;AAAA,EACA,iBAAiB,OAAO,YAAY;AAClC,UAAM,QAAQ,KAAK,UAAU;AAC7B,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA,OAAO,UAAU;AAAA,MACjB,MAAM,UAAU,QAAQ;AAAA,MACxB,MAAM,QAAQ,MAAM;AAAA,MACpB,KAAK,QAAQ,MAAM;AAAA,OAChB;AAAA,EAEP;AAAA,EACA,OAAO,OAAO,SAAS,iBAAiB,mBAAmB;AACzD,WAAO,KAAK,qBAAqB,WAAa,kBAAqB,MAAM,CAAC;AAAA,EAC5E;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,YAAY,GAAG,CAAC,mBAAmB,GAAG,CAAC,oBAAoB,GAAG,CAAC,mBAAmB,CAAC;AAAA,IAChG,gBAAgB,SAAS,wBAAwB,IAAI,KAAK,UAAU;AAClE,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,KAAK,CAAC;AAClC,QAAG,eAAe,UAAU,KAAK,CAAC;AAClC,QAAG,eAAe,UAAU,KAAK,CAAC;AAClC,QAAG,eAAe,UAAU,KAAK,CAAC;AAClC,QAAG,eAAe,UAAU,eAAe,CAAC;AAAA,MAC9C;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,kBAAkB,GAAG;AACtE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,eAAe,GAAG;AACnE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,iBAAiB,GAAG;AACrE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,qBAAqB,GAAG;AACzE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,YAAY;AAAA,MAC/D;AAAA,IACF;AAAA,IACA,WAAW,SAAS,eAAe,IAAI,KAAK;AAC1C,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,KAAK,CAAC;AACrB,QAAG,YAAY,KAAK,CAAC;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,mBAAmB,GAAG;AACvE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,mBAAmB,GAAG;AAAA,MACzE;AAAA,IACF;AAAA,IACA,UAAU;AAAA,IACV,cAAc,SAAS,sBAAsB,IAAI,KAAK;AACpD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,UAAU,IAAI,MAAM;AAAA,MACrC;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,MACP,UAAU;AAAA,MACV,cAAc;AAAA,MACd,aAAa;AAAA,MACb,aAAa;AAAA,MACb,MAAM;AAAA,MACN,OAAO;AAAA,MACP,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,mBAAmB;AAAA,MACnB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AAAA,IACA,SAAS;AAAA,MACP,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,qBAAqB;AAAA,IACvB;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC,aAAa,CAAC,GAAM,4BAA+B,oBAAoB;AAAA,IACzG,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,qBAAqB,EAAE,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC,kBAAkB,EAAE,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC,iBAAiB,EAAE,GAAG,CAAC,qBAAqB,EAAE,GAAG,CAAC,GAAG,QAAQ,UAAU,GAAG,CAAC,GAAG,UAAU,WAAW,SAAS,GAAG,CAAC,SAAS,4BAA4B,GAAG,WAAW,GAAG,MAAM,GAAG,CAAC,SAAS,4BAA4B,GAAG,WAAW,GAAG,MAAM,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,GAAG,CAAC,GAAG,SAAS,GAAG,CAAC,GAAG,SAAS,WAAW,cAAc,GAAG,CAAC,GAAG,4BAA4B,GAAG,SAAS,GAAG,CAAC,GAAG,4BAA4B,GAAG,SAAS,GAAG,CAAC,GAAG,SAAS,SAAS,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC,GAAG,MAAM,CAAC;AAAA,IAChlB,UAAU,SAAS,kBAAkB,IAAI,KAAK;AAC5C,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,WAAW,GAAG,kCAAkC,GAAG,IAAI,gBAAgB,CAAC,EAAE,GAAG,iCAAiC,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAAA,MAC1K;AACA,UAAI,KAAK,GAAG;AACV,cAAM,uBAA0B,YAAY,CAAC;AAC7C,QAAG,WAAW,QAAQ,CAAC,IAAI,SAAS,EAAE,YAAY,oBAAoB;AAAA,MACxE;AAAA,IACF;AAAA,IACA,cAAc,CAAC,cAAiB,SAAY,SAAY,MAAS,kBAAqB,SAAS,aAAa,YAAY;AAAA,IACxH,eAAe;AAAA,EACjB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,UAAU,CAAC;AAAA,IACjF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,SAAS,CAAC,cAAc,aAAa,YAAY;AAAA,MACjD,YAAY;AAAA,MACZ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA4DV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,WAAW,CAAC,aAAa;AAAA,IAC3B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,IAAI,CAAC;AAAA,MACH,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,IAClB,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,IAClB,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,IACvB,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,QAChB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,QACb,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,QACf,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,QACnB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,OAAO,OAAO,SAAS,uBAAuB,mBAAmB;AAC/D,WAAO,KAAK,qBAAqB,iBAAgB;AAAA,EACnD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,UAAU,YAAY;AAAA,IAChC,SAAS,CAAC,UAAU,YAAY;AAAA,EAClC,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,UAAU,cAAc,YAAY;AAAA,EAChD,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,UAAU,YAAY;AAAA,MAChC,SAAS,CAAC,UAAU,YAAY;AAAA,IAClC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["ScrollerClasses"]}