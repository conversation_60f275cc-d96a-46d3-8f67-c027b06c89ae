package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.controller;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.UsuarioEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service.UsuarioService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/usuarios")
@CrossOrigin(origins = { "http://localhost:4200", "https://localhost:4200" })
public class UsuarioController {

    @Autowired
    private UsuarioService usuarioService;

    // Obtener todos los usuarios (para tabla principal)
    @GetMapping("/consultar")
    public ResponseEntity<List<UsuarioEntity>> getAllUsuarios() {
        return ResponseEntity.ok(usuarioService.buscarTodos());
    }

    // Crear un nuevo usuario
    @PostMapping
    public ResponseEntity<UsuarioEntity> crearUsuario(@RequestBody UsuarioEntity nuevoUsuario) {
        if (nuevoUsuario.getUsername() == null || nuevoUsuario.getUsername().isEmpty()) {
            return ResponseEntity.badRequest().build();
        }
        UsuarioEntity creado = usuarioService.crearUsuario(nuevoUsuario);
        return ResponseEntity.status(201).body(creado);
    }

    // Eliminar un usuario por su username
    @DeleteMapping("/{username}")
    public ResponseEntity<Void> eliminarUsuario(@PathVariable String username) {
        boolean eliminado = usuarioService.eliminarUsuario(username);
        return eliminado ? ResponseEntity.noContent().build() : ResponseEntity.notFound().build();
    }

    // Buscar un usuario por su username
    @GetMapping("/{username}")
    public ResponseEntity<UsuarioEntity> buscarUsuarioPorId(@PathVariable String username) {
        return usuarioService.buscarPorUsername(username)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    // Modificar el estado de un usuario a activo
    @PutMapping("/activar/{username}")
    public ResponseEntity<UsuarioEntity> activarUsuario(@PathVariable String username) {
        UsuarioEntity usuario = new UsuarioEntity();
        usuario.setUsername(username);
        usuario.setEstado("activo");
        return ResponseEntity.ok(usuarioService.actualizarEstado(usuario));
    }

    // Modificar el estado de un usuario a inactivo
    @PutMapping("/desactivar/{username}")
    public ResponseEntity<UsuarioEntity> desactivarUsuario(@PathVariable String username) {
        UsuarioEntity usuario = new UsuarioEntity();
        usuario.setUsername(username);
        usuario.setEstado("inactivo");
        return ResponseEntity.ok(usuarioService.actualizarEstado(usuario));
    }

    // Buscar usuarios por rol y estado
    @GetMapping("/buscar")
    public ResponseEntity<List<UsuarioEntity>> buscarPorRolYEstado(
            @RequestParam(required = false) String rol,
            @RequestParam(required = false) String estado) {
        List<UsuarioEntity> resultado = usuarioService.buscarUsuariosPorRolYEstado(rol, estado);
        return ResponseEntity.ok(resultado);
    }

    // Asignar roles a un usuario por su username
    @PutMapping("/{username}/roles")
    public ResponseEntity<UsuarioEntity> actualizarRoles(
            @PathVariable String username,
            @RequestBody List<Long> idsRoles) {
        return ResponseEntity.ok(usuarioService.asignarRoles(username, idsRoles));
    }

}
