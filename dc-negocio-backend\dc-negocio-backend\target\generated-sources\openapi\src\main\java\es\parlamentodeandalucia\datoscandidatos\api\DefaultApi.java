/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (7.0.1).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
package es.parlamentodeandalucia.datoscandidatos.api;

import es.parlamentodeandalucia.datoscandidatos.dto.Candidato;
import es.parlamentodeandalucia.datoscandidatos.dto.Candidatura;
import es.parlamentodeandalucia.datoscandidatos.dto.CandidaturaInput;
import es.parlamentodeandalucia.datoscandidatos.dto.Circunscripcion;
import es.parlamentodeandalucia.datoscandidatos.dto.Convocatoria;
import es.parlamentodeandalucia.datoscandidatos.dto.FormacionPolitica;
import es.parlamentodeandalucia.datoscandidatos.dto.FormacionPoliticaInput;
import es.parlamentodeandalucia.datoscandidatos.dto.Importacion;
import es.parlamentodeandalucia.datoscandidatos.dto.ImportacionesProcesarCsvPost200Response;
import es.parlamentodeandalucia.datoscandidatos.dto.ImportacionesProcesarCsvPost400Response;
import es.parlamentodeandalucia.datoscandidatos.dto.Plantilla;
import es.parlamentodeandalucia.datoscandidatos.dto.Representante;
import es.parlamentodeandalucia.datoscandidatos.dto.Rol;
import es.parlamentodeandalucia.datoscandidatos.dto.Usuario;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import jakarta.annotation.Generated;

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2025-08-18T11:59:39.046397400+02:00[Europe/Madrid]")
@Validated
@Tag(name = "Default", description = "the Default API")
public interface DefaultApi {

    /**
     * GET /auth/me : Recuperar información del usuario autenticado
     * Obtiene la información del usuario actual desde el token JWT
     *
     * @return Información del usuario autenticado (status code 200)
     *         or Token no válido o expirado (status code 401)
     */
    @Operation(
        operationId = "authMeGet",
        summary = "Recuperar información del usuario autenticado",
        description = "Obtiene la información del usuario actual desde el token JWT",
        responses = {
            @ApiResponse(responseCode = "200", description = "Información del usuario autenticado", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = Usuario.class))
            }),
            @ApiResponse(responseCode = "401", description = "Token no válido o expirado")
        },
        security = {
            @SecurityRequirement(name = "bearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/auth/me",
        produces = { "application/json" }
    )
    ResponseEntity<Usuario> authMeGet(
        
    );


    /**
     * GET /candidatos : Listado de candidatos
     *
     * @return Lista de candidatos (status code 200)
     */
    @Operation(
        operationId = "candidatosGet",
        summary = "Listado de candidatos",
        responses = {
            @ApiResponse(responseCode = "200", description = "Lista de candidatos", content = {
                @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = Candidato.class)))
            })
        },
        security = {
            @SecurityRequirement(name = "bearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/candidatos",
        produces = { "application/json" }
    )
    ResponseEntity<List<Candidato>> candidatosGet(
        
    );


    /**
     * POST /candidatos : Alta de candidato
     *
     * @param candidato  (required)
     * @return Candidato creado correctamente (status code 201)
     */
    @Operation(
        operationId = "candidatosPost",
        summary = "Alta de candidato",
        responses = {
            @ApiResponse(responseCode = "201", description = "Candidato creado correctamente")
        },
        security = {
            @SecurityRequirement(name = "bearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/candidatos",
        consumes = { "application/json" }
    )
    ResponseEntity<Void> candidatosPost(
        @Parameter(name = "Candidato", description = "", required = true) @Valid @RequestBody Candidato candidato
    );


    /**
     * GET /candidaturas : Listar todas las candidaturas
     * Obtiene la lista completa de candidaturas con información de formación política y circunscripción
     *
     * @return Lista de candidaturas (status code 200)
     *         or No autorizado (status code 401)
     */
    @Operation(
        operationId = "candidaturasGet",
        summary = "Listar todas las candidaturas",
        description = "Obtiene la lista completa de candidaturas con información de formación política y circunscripción",
        responses = {
            @ApiResponse(responseCode = "200", description = "Lista de candidaturas", content = {
                @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = Candidatura.class)))
            }),
            @ApiResponse(responseCode = "401", description = "No autorizado")
        },
        security = {
            @SecurityRequirement(name = "bearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/candidaturas",
        produces = { "application/json" }
    )
    ResponseEntity<List<Candidatura>> candidaturasGet(
        
    );


    /**
     * DELETE /candidaturas/{id} : Eliminar candidatura
     * Elimina una candidatura (requiere rol ADMIN)
     *
     * @param id  (required)
     * @return Candidatura eliminada exitosamente (status code 204)
     *         or Candidatura no encontrada (status code 404)
     *         or No autorizado (status code 401)
     *         or Sin permisos (requiere rol ADMIN) (status code 403)
     */
    @Operation(
        operationId = "candidaturasIdDelete",
        summary = "Eliminar candidatura",
        description = "Elimina una candidatura (requiere rol ADMIN)",
        responses = {
            @ApiResponse(responseCode = "204", description = "Candidatura eliminada exitosamente"),
            @ApiResponse(responseCode = "404", description = "Candidatura no encontrada"),
            @ApiResponse(responseCode = "401", description = "No autorizado"),
            @ApiResponse(responseCode = "403", description = "Sin permisos (requiere rol ADMIN)")
        },
        security = {
            @SecurityRequirement(name = "bearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.DELETE,
        value = "/candidaturas/{id}"
    )
    ResponseEntity<Void> candidaturasIdDelete(
        @Parameter(name = "id", description = "", required = true, in = ParameterIn.PATH) @PathVariable("id") Long id
    );


    /**
     * GET /candidaturas/{id} : Obtener candidatura por ID
     * Obtiene los detalles de una candidatura específica
     *
     * @param id  (required)
     * @return Detalles de la candidatura (status code 200)
     *         or Candidatura no encontrada (status code 404)
     *         or No autorizado (status code 401)
     */
    @Operation(
        operationId = "candidaturasIdGet",
        summary = "Obtener candidatura por ID",
        description = "Obtiene los detalles de una candidatura específica",
        responses = {
            @ApiResponse(responseCode = "200", description = "Detalles de la candidatura", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = Candidatura.class))
            }),
            @ApiResponse(responseCode = "404", description = "Candidatura no encontrada"),
            @ApiResponse(responseCode = "401", description = "No autorizado")
        },
        security = {
            @SecurityRequirement(name = "bearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/candidaturas/{id}",
        produces = { "application/json" }
    )
    ResponseEntity<Candidatura> candidaturasIdGet(
        @Parameter(name = "id", description = "", required = true, in = ParameterIn.PATH) @PathVariable("id") Long id
    );


    /**
     * PUT /candidaturas/{id} : Actualizar candidatura
     * Actualiza una candidatura existente (requiere rol ADMIN)
     *
     * @param id  (required)
     * @param candidaturaInput  (required)
     * @return Candidatura actualizada exitosamente (status code 200)
     *         or Datos inválidos (status code 400)
     *         or Candidatura no encontrada (status code 404)
     *         or No autorizado (status code 401)
     *         or Sin permisos (requiere rol ADMIN) (status code 403)
     */
    @Operation(
        operationId = "candidaturasIdPut",
        summary = "Actualizar candidatura",
        description = "Actualiza una candidatura existente (requiere rol ADMIN)",
        responses = {
            @ApiResponse(responseCode = "200", description = "Candidatura actualizada exitosamente", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = Candidatura.class))
            }),
            @ApiResponse(responseCode = "400", description = "Datos inválidos"),
            @ApiResponse(responseCode = "404", description = "Candidatura no encontrada"),
            @ApiResponse(responseCode = "401", description = "No autorizado"),
            @ApiResponse(responseCode = "403", description = "Sin permisos (requiere rol ADMIN)")
        },
        security = {
            @SecurityRequirement(name = "bearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.PUT,
        value = "/candidaturas/{id}",
        produces = { "application/json" },
        consumes = { "application/json" }
    )
    ResponseEntity<Candidatura> candidaturasIdPut(
        @Parameter(name = "id", description = "", required = true, in = ParameterIn.PATH) @PathVariable("id") Long id,
        @Parameter(name = "CandidaturaInput", description = "", required = true) @Valid @RequestBody CandidaturaInput candidaturaInput
    );


    /**
     * POST /candidaturas/{id}/rechazar : Rechazar candidatura
     * Rechaza una candidatura con observaciones (requiere rol ADMIN)
     *
     * @param id  (required)
     * @param observacion  (required)
     * @return Candidatura rechazada exitosamente (status code 200)
     *         or Candidatura no encontrada (status code 404)
     *         or No autorizado (status code 401)
     *         or Sin permisos (requiere rol ADMIN) (status code 403)
     */
    @Operation(
        operationId = "candidaturasIdRechazarPost",
        summary = "Rechazar candidatura",
        description = "Rechaza una candidatura con observaciones (requiere rol ADMIN)",
        responses = {
            @ApiResponse(responseCode = "200", description = "Candidatura rechazada exitosamente", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = Candidatura.class))
            }),
            @ApiResponse(responseCode = "404", description = "Candidatura no encontrada"),
            @ApiResponse(responseCode = "401", description = "No autorizado"),
            @ApiResponse(responseCode = "403", description = "Sin permisos (requiere rol ADMIN)")
        },
        security = {
            @SecurityRequirement(name = "bearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/candidaturas/{id}/rechazar",
        produces = { "application/json" }
    )
    ResponseEntity<Candidatura> candidaturasIdRechazarPost(
        @Parameter(name = "id", description = "", required = true, in = ParameterIn.PATH) @PathVariable("id") Long id,
        @NotNull @Parameter(name = "observacion", description = "", required = true, in = ParameterIn.QUERY) @Valid @RequestParam(value = "observacion", required = true) String observacion
    );


    /**
     * POST /candidaturas/{id}/validar : Validar candidatura
     * Valida una candidatura por parte de la JEA (requiere rol ADMIN)
     *
     * @param id  (required)
     * @param comentario  (optional)
     * @return Candidatura validada exitosamente (status code 200)
     *         or Candidatura no encontrada (status code 404)
     *         or No autorizado (status code 401)
     *         or Sin permisos (requiere rol ADMIN) (status code 403)
     */
    @Operation(
        operationId = "candidaturasIdValidarPost",
        summary = "Validar candidatura",
        description = "Valida una candidatura por parte de la JEA (requiere rol ADMIN)",
        responses = {
            @ApiResponse(responseCode = "200", description = "Candidatura validada exitosamente", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = Candidatura.class))
            }),
            @ApiResponse(responseCode = "404", description = "Candidatura no encontrada"),
            @ApiResponse(responseCode = "401", description = "No autorizado"),
            @ApiResponse(responseCode = "403", description = "Sin permisos (requiere rol ADMIN)")
        },
        security = {
            @SecurityRequirement(name = "bearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/candidaturas/{id}/validar",
        produces = { "application/json" }
    )
    ResponseEntity<Candidatura> candidaturasIdValidarPost(
        @Parameter(name = "id", description = "", required = true, in = ParameterIn.PATH) @PathVariable("id") Long id,
        @Parameter(name = "comentario", description = "", in = ParameterIn.QUERY) @Valid @RequestParam(value = "comentario", required = false) String comentario
    );


    /**
     * POST /candidaturas : Crear nueva candidatura
     * Crea una nueva candidatura (requiere rol ADMIN)
     *
     * @param candidaturaInput  (required)
     * @return Candidatura creada exitosamente (status code 201)
     *         or Datos inválidos (status code 400)
     *         or No autorizado (status code 401)
     *         or Sin permisos (requiere rol ADMIN) (status code 403)
     */
    @Operation(
        operationId = "candidaturasPost",
        summary = "Crear nueva candidatura",
        description = "Crea una nueva candidatura (requiere rol ADMIN)",
        responses = {
            @ApiResponse(responseCode = "201", description = "Candidatura creada exitosamente", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = Candidatura.class))
            }),
            @ApiResponse(responseCode = "400", description = "Datos inválidos"),
            @ApiResponse(responseCode = "401", description = "No autorizado"),
            @ApiResponse(responseCode = "403", description = "Sin permisos (requiere rol ADMIN)")
        },
        security = {
            @SecurityRequirement(name = "bearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/candidaturas",
        produces = { "application/json" },
        consumes = { "application/json" }
    )
    ResponseEntity<Candidatura> candidaturasPost(
        @Parameter(name = "CandidaturaInput", description = "", required = true) @Valid @RequestBody CandidaturaInput candidaturaInput
    );


    /**
     * GET /circunscripciones : Listado de circunscripciones
     *
     * @return Lista de circunscripciones (status code 200)
     */
    @Operation(
        operationId = "circunscripcionesGet",
        summary = "Listado de circunscripciones",
        responses = {
            @ApiResponse(responseCode = "200", description = "Lista de circunscripciones", content = {
                @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = Circunscripcion.class)))
            })
        },
        security = {
            @SecurityRequirement(name = "bearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/circunscripciones",
        produces = { "application/json" }
    )
    ResponseEntity<List<Circunscripcion>> circunscripcionesGet(
        
    );


    /**
     * DELETE /circunscripciones/{id} : Baja de circunscripción
     *
     * @param id  (required)
     * @return Circunscripción eliminada correctamente (status code 204)
     */
    @Operation(
        operationId = "circunscripcionesIdDelete",
        summary = "Baja de circunscripción",
        responses = {
            @ApiResponse(responseCode = "204", description = "Circunscripción eliminada correctamente")
        },
        security = {
            @SecurityRequirement(name = "bearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.DELETE,
        value = "/circunscripciones/{id}"
    )
    ResponseEntity<Void> circunscripcionesIdDelete(
        @Parameter(name = "id", description = "", required = true, in = ParameterIn.PATH) @PathVariable("id") Integer id
    );


    /**
     * GET /circunscripciones/{id} : Consulta de circunscripción
     *
     * @param id  (required)
     * @return Circunscripción encontrada (status code 200)
     */
    @Operation(
        operationId = "circunscripcionesIdGet",
        summary = "Consulta de circunscripción",
        responses = {
            @ApiResponse(responseCode = "200", description = "Circunscripción encontrada", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = Circunscripcion.class))
            })
        },
        security = {
            @SecurityRequirement(name = "bearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/circunscripciones/{id}",
        produces = { "application/json" }
    )
    ResponseEntity<Circunscripcion> circunscripcionesIdGet(
        @Parameter(name = "id", description = "", required = true, in = ParameterIn.PATH) @PathVariable("id") Integer id
    );


    /**
     * PUT /circunscripciones/{id} : Modificación de circunscripción
     *
     * @param id  (required)
     * @param circunscripcion  (required)
     * @return Circunscripción actualizada correctamente (status code 200)
     */
    @Operation(
        operationId = "circunscripcionesIdPut",
        summary = "Modificación de circunscripción",
        responses = {
            @ApiResponse(responseCode = "200", description = "Circunscripción actualizada correctamente")
        },
        security = {
            @SecurityRequirement(name = "bearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.PUT,
        value = "/circunscripciones/{id}",
        consumes = { "application/json" }
    )
    ResponseEntity<Void> circunscripcionesIdPut(
        @Parameter(name = "id", description = "", required = true, in = ParameterIn.PATH) @PathVariable("id") Integer id,
        @Parameter(name = "Circunscripcion", description = "", required = true) @Valid @RequestBody Circunscripcion circunscripcion
    );


    /**
     * POST /circunscripciones : Alta de circunscripción
     *
     * @param circunscripcion  (required)
     * @return Circunscripción creada correctamente (status code 201)
     */
    @Operation(
        operationId = "circunscripcionesPost",
        summary = "Alta de circunscripción",
        responses = {
            @ApiResponse(responseCode = "201", description = "Circunscripción creada correctamente")
        },
        security = {
            @SecurityRequirement(name = "bearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/circunscripciones",
        consumes = { "application/json" }
    )
    ResponseEntity<Void> circunscripcionesPost(
        @Parameter(name = "Circunscripcion", description = "", required = true) @Valid @RequestBody Circunscripcion circunscripcion
    );


    /**
     * GET /convocatorias : Listado de convocatorias
     *
     * @return Lista de convocatorias (status code 200)
     */
    @Operation(
        operationId = "convocatoriasGet",
        summary = "Listado de convocatorias",
        responses = {
            @ApiResponse(responseCode = "200", description = "Lista de convocatorias", content = {
                @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = Convocatoria.class)))
            })
        },
        security = {
            @SecurityRequirement(name = "bearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/convocatorias",
        produces = { "application/json" }
    )
    ResponseEntity<List<Convocatoria>> convocatoriasGet(
        
    );


    /**
     * DELETE /convocatorias/{id} : Baja de convocatoria
     *
     * @param id  (required)
     * @return Convocatoria eliminada correctamente (status code 204)
     */
    @Operation(
        operationId = "convocatoriasIdDelete",
        summary = "Baja de convocatoria",
        responses = {
            @ApiResponse(responseCode = "204", description = "Convocatoria eliminada correctamente")
        },
        security = {
            @SecurityRequirement(name = "bearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.DELETE,
        value = "/convocatorias/{id}"
    )
    ResponseEntity<Void> convocatoriasIdDelete(
        @Parameter(name = "id", description = "", required = true, in = ParameterIn.PATH) @PathVariable("id") Integer id
    );


    /**
     * GET /convocatorias/{id} : Consulta de convocatoria
     *
     * @param id  (required)
     * @return Convocatoria encontrada (status code 200)
     */
    @Operation(
        operationId = "convocatoriasIdGet",
        summary = "Consulta de convocatoria",
        responses = {
            @ApiResponse(responseCode = "200", description = "Convocatoria encontrada", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = Convocatoria.class))
            })
        },
        security = {
            @SecurityRequirement(name = "bearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/convocatorias/{id}",
        produces = { "application/json" }
    )
    ResponseEntity<Convocatoria> convocatoriasIdGet(
        @Parameter(name = "id", description = "", required = true, in = ParameterIn.PATH) @PathVariable("id") Integer id
    );


    /**
     * PUT /convocatorias/{id} : Modificación de convocatoria
     *
     * @param id  (required)
     * @param convocatoria  (required)
     * @return Convocatoria actualizada correctamente (status code 200)
     */
    @Operation(
        operationId = "convocatoriasIdPut",
        summary = "Modificación de convocatoria",
        responses = {
            @ApiResponse(responseCode = "200", description = "Convocatoria actualizada correctamente")
        },
        security = {
            @SecurityRequirement(name = "bearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.PUT,
        value = "/convocatorias/{id}",
        consumes = { "application/json" }
    )
    ResponseEntity<Void> convocatoriasIdPut(
        @Parameter(name = "id", description = "", required = true, in = ParameterIn.PATH) @PathVariable("id") Integer id,
        @Parameter(name = "Convocatoria", description = "", required = true) @Valid @RequestBody Convocatoria convocatoria
    );


    /**
     * POST /convocatorias : Alta de convocatoria
     *
     * @param convocatoria  (required)
     * @return Convocatoria creada correctamente (status code 201)
     */
    @Operation(
        operationId = "convocatoriasPost",
        summary = "Alta de convocatoria",
        responses = {
            @ApiResponse(responseCode = "201", description = "Convocatoria creada correctamente")
        },
        security = {
            @SecurityRequirement(name = "bearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/convocatorias",
        consumes = { "application/json" }
    )
    ResponseEntity<Void> convocatoriasPost(
        @Parameter(name = "Convocatoria", description = "", required = true) @Valid @RequestBody Convocatoria convocatoria
    );


    /**
     * GET /formaciones-politicas : Listado de formaciones políticas
     *
     * @return Lista de formaciones políticas (status code 200)
     */
    @Operation(
        operationId = "formacionesPoliticasGet",
        summary = "Listado de formaciones políticas",
        responses = {
            @ApiResponse(responseCode = "200", description = "Lista de formaciones políticas", content = {
                @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = FormacionPolitica.class)))
            })
        },
        security = {
            @SecurityRequirement(name = "bearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/formaciones-politicas",
        produces = { "application/json" }
    )
    ResponseEntity<List<FormacionPolitica>> formacionesPoliticasGet(
        
    );


    /**
     * DELETE /formaciones-politicas/{id} : Eliminación de formación política
     *
     * @param id  (required)
     * @return Formación política eliminada correctamente (status code 204)
     *         or No se puede eliminar, tiene candidaturas asociadas (status code 409)
     *         or Formación política no encontrada (status code 404)
     */
    @Operation(
        operationId = "formacionesPoliticasIdDelete",
        summary = "Eliminación de formación política",
        responses = {
            @ApiResponse(responseCode = "204", description = "Formación política eliminada correctamente"),
            @ApiResponse(responseCode = "409", description = "No se puede eliminar, tiene candidaturas asociadas"),
            @ApiResponse(responseCode = "404", description = "Formación política no encontrada")
        },
        security = {
            @SecurityRequirement(name = "bearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.DELETE,
        value = "/formaciones-politicas/{id}"
    )
    ResponseEntity<Void> formacionesPoliticasIdDelete(
        @Parameter(name = "id", description = "", required = true, in = ParameterIn.PATH) @PathVariable("id") Long id
    );


    /**
     * GET /formaciones-politicas/{id} : Obtener una formación política por ID
     *
     * @param id  (required)
     * @return Formación política encontrada (status code 200)
     *         or Formación política no encontrada (status code 404)
     */
    @Operation(
        operationId = "formacionesPoliticasIdGet",
        summary = "Obtener una formación política por ID",
        responses = {
            @ApiResponse(responseCode = "200", description = "Formación política encontrada", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = FormacionPolitica.class))
            }),
            @ApiResponse(responseCode = "404", description = "Formación política no encontrada")
        },
        security = {
            @SecurityRequirement(name = "bearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/formaciones-politicas/{id}",
        produces = { "application/json" }
    )
    ResponseEntity<FormacionPolitica> formacionesPoliticasIdGet(
        @Parameter(name = "id", description = "", required = true, in = ParameterIn.PATH) @PathVariable("id") Long id
    );


    /**
     * PUT /formaciones-politicas/{id} : Modificación de formación política
     *
     * @param id  (required)
     * @param formacionPoliticaInput  (required)
     * @return Formación política actualizada correctamente (status code 200)
     *         or Formación política no encontrada (status code 404)
     *         or Datos duplicados o inválidos (status code 400)
     */
    @Operation(
        operationId = "formacionesPoliticasIdPut",
        summary = "Modificación de formación política",
        responses = {
            @ApiResponse(responseCode = "200", description = "Formación política actualizada correctamente"),
            @ApiResponse(responseCode = "404", description = "Formación política no encontrada"),
            @ApiResponse(responseCode = "400", description = "Datos duplicados o inválidos")
        },
        security = {
            @SecurityRequirement(name = "bearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.PUT,
        value = "/formaciones-politicas/{id}",
        consumes = { "application/json" }
    )
    ResponseEntity<Void> formacionesPoliticasIdPut(
        @Parameter(name = "id", description = "", required = true, in = ParameterIn.PATH) @PathVariable("id") Long id,
        @Parameter(name = "FormacionPoliticaInput", description = "", required = true) @Valid @RequestBody FormacionPoliticaInput formacionPoliticaInput
    );


    /**
     * POST /formaciones-politicas : Alta de formación política
     *
     * @param formacionPoliticaInput  (required)
     * @return Formación política creada correctamente (status code 201)
     *         or Datos duplicados o inválidos (status code 400)
     */
    @Operation(
        operationId = "formacionesPoliticasPost",
        summary = "Alta de formación política",
        responses = {
            @ApiResponse(responseCode = "201", description = "Formación política creada correctamente"),
            @ApiResponse(responseCode = "400", description = "Datos duplicados o inválidos")
        },
        security = {
            @SecurityRequirement(name = "bearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/formaciones-politicas",
        consumes = { "application/json" }
    )
    ResponseEntity<Void> formacionesPoliticasPost(
        @Parameter(name = "FormacionPoliticaInput", description = "", required = true) @Valid @RequestBody FormacionPoliticaInput formacionPoliticaInput
    );


    /**
     * GET /importaciones : Listar importaciones
     * Obtiene la lista de todas las importaciones realizadas
     *
     * @return Lista de importaciones (status code 200)
     *         or No autorizado (status code 401)
     */
    @Operation(
        operationId = "importacionesGet",
        summary = "Listar importaciones",
        description = "Obtiene la lista de todas las importaciones realizadas",
        responses = {
            @ApiResponse(responseCode = "200", description = "Lista de importaciones", content = {
                @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = Importacion.class)))
            }),
            @ApiResponse(responseCode = "401", description = "No autorizado")
        },
        security = {
            @SecurityRequirement(name = "bearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/importaciones",
        produces = { "application/json" }
    )
    ResponseEntity<List<Importacion>> importacionesGet(
        
    );


    /**
     * DELETE /importaciones/{id} : Eliminar importación
     * Elimina una importación (requiere rol ADMIN)
     *
     * @param id  (required)
     * @return Importación eliminada exitosamente (status code 200)
     *         or Importación no encontrada (status code 404)
     *         or No autorizado (status code 401)
     *         or Sin permisos (requiere rol ADMIN) (status code 403)
     */
    @Operation(
        operationId = "importacionesIdDelete",
        summary = "Eliminar importación",
        description = "Elimina una importación (requiere rol ADMIN)",
        responses = {
            @ApiResponse(responseCode = "200", description = "Importación eliminada exitosamente"),
            @ApiResponse(responseCode = "404", description = "Importación no encontrada"),
            @ApiResponse(responseCode = "401", description = "No autorizado"),
            @ApiResponse(responseCode = "403", description = "Sin permisos (requiere rol ADMIN)")
        },
        security = {
            @SecurityRequirement(name = "bearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.DELETE,
        value = "/importaciones/{id}"
    )
    ResponseEntity<Void> importacionesIdDelete(
        @Parameter(name = "id", description = "", required = true, in = ParameterIn.PATH) @PathVariable("id") Long id
    );


    /**
     * GET /importaciones/{id} : Obtener importación por ID
     * Obtiene los detalles de una importación específica
     *
     * @param id  (required)
     * @return Detalles de la importación (status code 200)
     *         or Importación no encontrada (status code 404)
     *         or No autorizado (status code 401)
     */
    @Operation(
        operationId = "importacionesIdGet",
        summary = "Obtener importación por ID",
        description = "Obtiene los detalles de una importación específica",
        responses = {
            @ApiResponse(responseCode = "200", description = "Detalles de la importación", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = Importacion.class))
            }),
            @ApiResponse(responseCode = "404", description = "Importación no encontrada"),
            @ApiResponse(responseCode = "401", description = "No autorizado")
        },
        security = {
            @SecurityRequirement(name = "bearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/importaciones/{id}",
        produces = { "application/json" }
    )
    ResponseEntity<Importacion> importacionesIdGet(
        @Parameter(name = "id", description = "", required = true, in = ParameterIn.PATH) @PathVariable("id") Long id
    );


    /**
     * POST /importaciones/procesar-csv : Importar candidatos desde archivo CSV
     * Procesa un archivo CSV para importar candidatos y candidaturas (requiere rol ADMIN)
     *
     * @param archivo Archivo CSV (.csv) con los datos de candidatos en formato específico (optional)
     * @return Archivo procesado exitosamente (status code 200)
     *         or Archivo inválido o error en el procesamiento (status code 400)
     *         or No autorizado (status code 401)
     *         or Sin permisos (requiere rol ADMIN) (status code 403)
     */
    @Operation(
        operationId = "importacionesProcesarCsvPost",
        summary = "Importar candidatos desde archivo CSV",
        description = "Procesa un archivo CSV para importar candidatos y candidaturas (requiere rol ADMIN)",
        responses = {
            @ApiResponse(responseCode = "200", description = "Archivo procesado exitosamente", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = ImportacionesProcesarCsvPost200Response.class))
            }),
            @ApiResponse(responseCode = "400", description = "Archivo inválido o error en el procesamiento", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = ImportacionesProcesarCsvPost400Response.class))
            }),
            @ApiResponse(responseCode = "401", description = "No autorizado"),
            @ApiResponse(responseCode = "403", description = "Sin permisos (requiere rol ADMIN)")
        },
        security = {
            @SecurityRequirement(name = "bearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/importaciones/procesar-csv",
        produces = { "application/json" },
        consumes = { "multipart/form-data" }
    )
    ResponseEntity<ImportacionesProcesarCsvPost200Response> importacionesProcesarCsvPost(
        @Parameter(name = "archivo", description = "Archivo CSV (.csv) con los datos de candidatos en formato específico") @RequestPart(value = "archivo", required = false) MultipartFile archivo
    );


    /**
     * GET /plantillas : Listado de plantillas disponibles
     *
     * @return Lista de plantillas (status code 200)
     */
    @Operation(
        operationId = "plantillasGet",
        summary = "Listado de plantillas disponibles",
        responses = {
            @ApiResponse(responseCode = "200", description = "Lista de plantillas", content = {
                @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = Plantilla.class)))
            })
        },
        security = {
            @SecurityRequirement(name = "bearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/plantillas",
        produces = { "application/json" }
    )
    ResponseEntity<List<Plantilla>> plantillasGet(
        
    );


    /**
     * DELETE /plantillas/{id} : Eliminación de plantilla
     *
     * @param id  (required)
     * @return Plantilla eliminada correctamente (status code 204)
     *         or Plantilla no encontrada (status code 404)
     */
    @Operation(
        operationId = "plantillasIdDelete",
        summary = "Eliminación de plantilla",
        responses = {
            @ApiResponse(responseCode = "204", description = "Plantilla eliminada correctamente"),
            @ApiResponse(responseCode = "404", description = "Plantilla no encontrada")
        },
        security = {
            @SecurityRequirement(name = "bearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.DELETE,
        value = "/plantillas/{id}"
    )
    ResponseEntity<Void> plantillasIdDelete(
        @Parameter(name = "id", description = "", required = true, in = ParameterIn.PATH) @PathVariable("id") Integer id
    );


    /**
     * GET /plantillas/{id} : Obtener una plantilla por ID
     *
     * @param id  (required)
     * @return Detalle de la plantilla (status code 200)
     *         or Plantilla no encontrada (status code 404)
     */
    @Operation(
        operationId = "plantillasIdGet",
        summary = "Obtener una plantilla por ID",
        responses = {
            @ApiResponse(responseCode = "200", description = "Detalle de la plantilla", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = Plantilla.class))
            }),
            @ApiResponse(responseCode = "404", description = "Plantilla no encontrada")
        },
        security = {
            @SecurityRequirement(name = "bearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/plantillas/{id}",
        produces = { "application/json" }
    )
    ResponseEntity<Plantilla> plantillasIdGet(
        @Parameter(name = "id", description = "", required = true, in = ParameterIn.PATH) @PathVariable("id") Integer id
    );


    /**
     * PUT /plantillas/{id} : Modificación de plantilla
     *
     * @param id  (required)
     * @param plantilla  (required)
     * @return Plantilla actualizada correctamente (status code 200)
     *         or Datos inválidos (status code 400)
     *         or Plantilla no encontrada (status code 404)
     */
    @Operation(
        operationId = "plantillasIdPut",
        summary = "Modificación de plantilla",
        responses = {
            @ApiResponse(responseCode = "200", description = "Plantilla actualizada correctamente"),
            @ApiResponse(responseCode = "400", description = "Datos inválidos"),
            @ApiResponse(responseCode = "404", description = "Plantilla no encontrada")
        },
        security = {
            @SecurityRequirement(name = "bearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.PUT,
        value = "/plantillas/{id}",
        consumes = { "application/json" }
    )
    ResponseEntity<Void> plantillasIdPut(
        @Parameter(name = "id", description = "", required = true, in = ParameterIn.PATH) @PathVariable("id") Integer id,
        @Parameter(name = "Plantilla", description = "", required = true) @Valid @RequestBody Plantilla plantilla
    );


    /**
     * POST /plantillas : Alta de plantilla
     *
     * @param plantilla  (required)
     * @return Plantilla creada correctamente (status code 201)
     *         or Datos inválidos (status code 400)
     */
    @Operation(
        operationId = "plantillasPost",
        summary = "Alta de plantilla",
        responses = {
            @ApiResponse(responseCode = "201", description = "Plantilla creada correctamente"),
            @ApiResponse(responseCode = "400", description = "Datos inválidos")
        },
        security = {
            @SecurityRequirement(name = "bearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/plantillas",
        consumes = { "application/json" }
    )
    ResponseEntity<Void> plantillasPost(
        @Parameter(name = "Plantilla", description = "", required = true) @Valid @RequestBody Plantilla plantilla
    );


    /**
     * GET /representantes : Listado de representantes
     *
     * @return Lista de representantes (status code 200)
     */
    @Operation(
        operationId = "representantesGet",
        summary = "Listado de representantes",
        responses = {
            @ApiResponse(responseCode = "200", description = "Lista de representantes", content = {
                @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = Representante.class)))
            })
        },
        security = {
            @SecurityRequirement(name = "bearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/representantes",
        produces = { "application/json" }
    )
    ResponseEntity<List<Representante>> representantesGet(
        
    );


    /**
     * GET /representantes/{id}/candidatos : Candidatos asociados a un representante
     *
     * @param id  (required)
     * @return Lista de candidatos asociados (status code 200)
     */
    @Operation(
        operationId = "representantesIdCandidatosGet",
        summary = "Candidatos asociados a un representante",
        responses = {
            @ApiResponse(responseCode = "200", description = "Lista de candidatos asociados", content = {
                @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = Candidato.class)))
            })
        },
        security = {
            @SecurityRequirement(name = "bearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/representantes/{id}/candidatos",
        produces = { "application/json" }
    )
    ResponseEntity<List<Candidato>> representantesIdCandidatosGet(
        @Parameter(name = "id", description = "", required = true, in = ParameterIn.PATH) @PathVariable("id") Integer id
    );


    /**
     * DELETE /representantes/{id} : Eliminar representante
     *
     * @param id  (required)
     * @return Representante eliminado correctamente (status code 204)
     */
    @Operation(
        operationId = "representantesIdDelete",
        summary = "Eliminar representante",
        responses = {
            @ApiResponse(responseCode = "204", description = "Representante eliminado correctamente")
        },
        security = {
            @SecurityRequirement(name = "bearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.DELETE,
        value = "/representantes/{id}"
    )
    ResponseEntity<Void> representantesIdDelete(
        @Parameter(name = "id", description = "", required = true, in = ParameterIn.PATH) @PathVariable("id") Integer id
    );


    /**
     * GET /representantes/{id} : Consulta de representante
     *
     * @param id  (required)
     * @return Representante encontrado (status code 200)
     */
    @Operation(
        operationId = "representantesIdGet",
        summary = "Consulta de representante",
        responses = {
            @ApiResponse(responseCode = "200", description = "Representante encontrado", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = Representante.class))
            })
        },
        security = {
            @SecurityRequirement(name = "bearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/representantes/{id}",
        produces = { "application/json" }
    )
    ResponseEntity<Representante> representantesIdGet(
        @Parameter(name = "id", description = "", required = true, in = ParameterIn.PATH) @PathVariable("id") Integer id
    );


    /**
     * PUT /representantes/{id} : Modificación de representante
     *
     * @param id  (required)
     * @param representante  (required)
     * @return Representante actualizado correctamente (status code 200)
     */
    @Operation(
        operationId = "representantesIdPut",
        summary = "Modificación de representante",
        responses = {
            @ApiResponse(responseCode = "200", description = "Representante actualizado correctamente")
        },
        security = {
            @SecurityRequirement(name = "bearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.PUT,
        value = "/representantes/{id}",
        consumes = { "application/json" }
    )
    ResponseEntity<Void> representantesIdPut(
        @Parameter(name = "id", description = "", required = true, in = ParameterIn.PATH) @PathVariable("id") Integer id,
        @Parameter(name = "Representante", description = "", required = true) @Valid @RequestBody Representante representante
    );


    /**
     * POST /representantes : Alta de representante
     *
     * @param representante  (required)
     * @return Representante creado correctamente (status code 201)
     */
    @Operation(
        operationId = "representantesPost",
        summary = "Alta de representante",
        responses = {
            @ApiResponse(responseCode = "201", description = "Representante creado correctamente")
        },
        security = {
            @SecurityRequirement(name = "bearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/representantes",
        consumes = { "application/json" }
    )
    ResponseEntity<Void> representantesPost(
        @Parameter(name = "Representante", description = "", required = true) @Valid @RequestBody Representante representante
    );


    /**
     * GET /roles : Listado de roles disponibles
     *
     * @return Lista de roles (status code 200)
     */
    @Operation(
        operationId = "rolesGet",
        summary = "Listado de roles disponibles",
        responses = {
            @ApiResponse(responseCode = "200", description = "Lista de roles", content = {
                @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = Rol.class)))
            })
        },
        security = {
            @SecurityRequirement(name = "bearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/roles",
        produces = { "application/json" }
    )
    ResponseEntity<List<Rol>> rolesGet(
        
    );


    /**
     * GET /usuarios : Listado de usuarios (filtros por rol, estado, etc.)
     *
     * @param rol  (optional)
     * @param estado  (optional)
     * @return Lista de usuarios (status code 200)
     */
    @Operation(
        operationId = "usuariosGet",
        summary = "Listado de usuarios (filtros por rol, estado, etc.)",
        responses = {
            @ApiResponse(responseCode = "200", description = "Lista de usuarios", content = {
                @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = Usuario.class)))
            })
        },
        security = {
            @SecurityRequirement(name = "bearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/usuarios",
        produces = { "application/json" }
    )
    ResponseEntity<List<Usuario>> usuariosGet(
        @Parameter(name = "rol", description = "", in = ParameterIn.QUERY) @Valid @RequestParam(value = "rol", required = false) String rol,
        @Parameter(name = "estado", description = "", in = ParameterIn.QUERY) @Valid @RequestParam(value = "estado", required = false) String estado
    );


    /**
     * DELETE /usuarios/{id} : Baja de usuario
     *
     * @param id  (required)
     * @return Usuario eliminado correctamente (status code 204)
     */
    @Operation(
        operationId = "usuariosIdDelete",
        summary = "Baja de usuario",
        responses = {
            @ApiResponse(responseCode = "204", description = "Usuario eliminado correctamente")
        },
        security = {
            @SecurityRequirement(name = "bearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.DELETE,
        value = "/usuarios/{id}"
    )
    ResponseEntity<Void> usuariosIdDelete(
        @Parameter(name = "id", description = "", required = true, in = ParameterIn.PATH) @PathVariable("id") Integer id
    );


    /**
     * GET /usuarios/{id} : Consulta de usuario
     *
     * @param id  (required)
     * @return Usuario encontrado (status code 200)
     */
    @Operation(
        operationId = "usuariosIdGet",
        summary = "Consulta de usuario",
        responses = {
            @ApiResponse(responseCode = "200", description = "Usuario encontrado", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = Usuario.class))
            })
        },
        security = {
            @SecurityRequirement(name = "bearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/usuarios/{id}",
        produces = { "application/json" }
    )
    ResponseEntity<Usuario> usuariosIdGet(
        @Parameter(name = "id", description = "", required = true, in = ParameterIn.PATH) @PathVariable("id") Integer id
    );


    /**
     * PUT /usuarios/{id} : Modificación de usuario
     *
     * @param id  (required)
     * @param usuario  (required)
     * @return Usuario actualizado correctamente (status code 200)
     */
    @Operation(
        operationId = "usuariosIdPut",
        summary = "Modificación de usuario",
        responses = {
            @ApiResponse(responseCode = "200", description = "Usuario actualizado correctamente")
        },
        security = {
            @SecurityRequirement(name = "bearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.PUT,
        value = "/usuarios/{id}",
        consumes = { "application/json" }
    )
    ResponseEntity<Void> usuariosIdPut(
        @Parameter(name = "id", description = "", required = true, in = ParameterIn.PATH) @PathVariable("id") Integer id,
        @Parameter(name = "Usuario", description = "", required = true) @Valid @RequestBody Usuario usuario
    );


    /**
     * POST /usuarios : Alta de usuario (admin)
     *
     * @param usuario  (required)
     * @return Usuario creado correctamente (status code 201)
     */
    @Operation(
        operationId = "usuariosPost",
        summary = "Alta de usuario (admin)",
        responses = {
            @ApiResponse(responseCode = "201", description = "Usuario creado correctamente")
        },
        security = {
            @SecurityRequirement(name = "bearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/usuarios",
        consumes = { "application/json" }
    )
    ResponseEntity<Void> usuariosPost(
        @Parameter(name = "Usuario", description = "", required = true) @Valid @RequestBody Usuario usuario
    );

}
